{% extends 'base.html' %}

{% block title %}Notifications - BlogApp{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-bell"></i> Notifications</h2>
    {% if notifications %}
    <a href="{% url 'mark_all_read' %}" class="btn btn-outline-primary">
        <i class="fas fa-check-double"></i> Mark All as Read
    </a>
    {% endif %}
</div>

{% if notifications %}
<div class="card">
    <div class="card-body p-0">
        {% for notification in notifications %}
        <div class="border-bottom p-3 {% if not notification.is_read %}bg-light{% endif %}">
            <div class="d-flex align-items-start">
                <!-- Notification Icon -->
                <div class="me-3">
                    {% if notification.notification_type == 'like' %}
                    <div class="bg-danger bg-opacity-10 rounded-circle p-2">
                        <i class="fas fa-heart text-danger"></i>
                    </div>
                    {% elif notification.notification_type == 'comment' %}
                    <div class="bg-primary bg-opacity-10 rounded-circle p-2">
                        <i class="fas fa-comment text-primary"></i>
                    </div>
                    {% elif notification.notification_type == 'follow' %}
                    <div class="bg-success bg-opacity-10 rounded-circle p-2">
                        <i class="fas fa-user-plus text-success"></i>
                    </div>
                    {% elif notification.notification_type == 'mention' %}
                    <div class="bg-warning bg-opacity-10 rounded-circle p-2">
                        <i class="fas fa-at text-warning"></i>
                    </div>
                    {% elif notification.notification_type == 'share' %}
                    <div class="bg-info bg-opacity-10 rounded-circle p-2">
                        <i class="fas fa-share text-info"></i>
                    </div>
                    {% else %}
                    <div class="bg-secondary bg-opacity-10 rounded-circle p-2">
                        <i class="fas fa-bell text-secondary"></i>
                    </div>
                    {% endif %}
                </div>
                
                <!-- Notification Content -->
                <div class="flex-grow-1">
                    <div class="d-flex align-items-center mb-1">
                        <img src="{{ notification.sender.profile.get_avatar_url }}"
                             alt="{{ notification.sender.username }}" class="avatar-sm me-2">
                        <div>
                            <strong>{{ notification.sender.username }}</strong>
                            {% if notification.sender.profile.is_verified %}
                            <i class="fas fa-check-circle text-primary ms-1" title="Verified"></i>
                            {% endif %}
                        </div>
                        <small class="text-muted ms-auto">
                            {{ notification.created_at|timesince }} ago
                        </small>
                    </div>
                    
                    <p class="mb-2">
                        {% if notification.notification_type == 'like' %}
                        liked your post
                        {% elif notification.notification_type == 'comment' %}
                        commented on your post
                        {% elif notification.notification_type == 'follow' %}
                        started following you
                        {% elif notification.notification_type == 'mention' %}
                        mentioned you in a post
                        {% elif notification.notification_type == 'share' %}
                        shared your post
                        {% else %}
                        {{ notification.message }}
                        {% endif %}
                    </p>
                    
                    {% if notification.post %}
                    <div class="bg-light rounded p-2 mb-2">
                        <small class="text-muted">
                            "{{ notification.post.content|truncatechars:100 }}"
                        </small>
                    </div>
                    {% endif %}
                    
                    <div class="d-flex gap-2">
                        {% if not notification.is_read %}
                        <a href="{% url 'mark_read' notification.pk %}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-check"></i> Mark Read
                        </a>
                        {% endif %}
                        
                        {% if notification.post %}
                        <a href="{% url 'post_detail' notification.post.pk %}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-eye"></i> View Post
                        </a>
                        {% endif %}
                        
                        {% if notification.notification_type == 'follow' %}
                        <a href="{% url 'profile' notification.sender.username %}" class="btn btn-sm btn-outline-info">
                            <i class="fas fa-user"></i> View Profile
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Pagination -->
{% if notifications.has_other_pages %}
<nav aria-label="Notifications pagination" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if notifications.has_previous %}
        <li class="page-item">
            <a class="page-link" href="?page={{ notifications.previous_page_number }}">
                <i class="fas fa-chevron-left"></i> Previous
            </a>
        </li>
        {% endif %}

        {% for num in notifications.paginator.page_range %}
            {% if notifications.number == num %}
            <li class="page-item active">
                <span class="page-link">{{ num }}</span>
            </li>
            {% elif num > notifications.number|add:'-3' and num < notifications.number|add:'3' %}
            <li class="page-item">
                <a class="page-link" href="?page={{ num }}">{{ num }}</a>
            </li>
            {% endif %}
        {% endfor %}

        {% if notifications.has_next %}
        <li class="page-item">
            <a class="page-link" href="?page={{ notifications.next_page_number }}">
                Next <i class="fas fa-chevron-right"></i>
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<!-- No Notifications -->
<div class="card">
    <div class="card-body text-center py-5">
        <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
        <h5>No notifications yet</h5>
        <p class="text-muted">
            When you get notifications, they'll show up here.
        </p>
        <a href="{% url 'home' %}" class="btn btn-primary">
            <i class="fas fa-home"></i> Go to Home
        </a>
    </div>
</div>
{% endif %}

<!-- Notification Settings -->
<div class="card mt-4">
    <div class="card-header">
        <h6 class="mb-0"><i class="fas fa-cog"></i> Notification Settings</h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="notify_likes" checked>
                    <label class="form-check-label" for="notify_likes">
                        <i class="fas fa-heart text-danger"></i> Likes
                    </label>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="notify_comments" checked>
                    <label class="form-check-label" for="notify_comments">
                        <i class="fas fa-comment text-primary"></i> Comments
                    </label>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="notify_follows" checked>
                    <label class="form-check-label" for="notify_follows">
                        <i class="fas fa-user-plus text-success"></i> New Followers
                    </label>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="notify_mentions" checked>
                    <label class="form-check-label" for="notify_mentions">
                        <i class="fas fa-at text-warning"></i> Mentions
                    </label>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="notify_shares" checked>
                    <label class="form-check-label" for="notify_shares">
                        <i class="fas fa-share text-info"></i> Shares
                    </label>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="notify_email" checked>
                    <label class="form-check-label" for="notify_email">
                        <i class="fas fa-envelope text-secondary"></i> Email Notifications
                    </label>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle notification settings checkboxes
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            // Here you would typically send an AJAX request to update settings
            console.log(`${this.id}: ${this.checked}`);
        });
    });
});
</script>
{% endblock %} 