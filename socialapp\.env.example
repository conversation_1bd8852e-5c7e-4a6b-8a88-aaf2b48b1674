# Django SocialApp Environment Configuration
# Copy this file to .env and update the values

# Django Settings
DJANGO_ENVIRONMENT=production
DJANGO_DEBUG=False
DJANGO_SECRET_KEY=your-super-secret-key-here-change-this-to-random-50-chars
DJ<PERSON><PERSON><PERSON>_ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com,*.railway.app,*.render.com,*.pythonanywhere.com
DJANGO_CSRF_TRUSTED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Database Configuration (PostgreSQL recommended for production)
DJANGO_DB_ENGINE=django.db.backends.postgresql
DJANGO_DB_NAME=socialapp_db
DJANGO_DB_USER=your_db_user
DJANGO_DB_PASSWORD=your_db_password
DJANGO_DB_HOST=localhost
DJANGO_DB_PORT=5432

# Redis Configuration (for real-time chat)
REDIS_URL=redis://localhost:6379/0

# Email Configuration (optional)
DJANGO_EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
DJANGO_EMAIL_HOST=smtp.gmail.com
DJANGO_EMAIL_PORT=587
DJANGO_EMAIL_USE_TLS=True
DJANGO_EMAIL_HOST_USER=<EMAIL>
DJANGO_EMAIL_HOST_PASSWORD=your-app-password
DJANGO_DEFAULT_FROM_EMAIL=<EMAIL>

# Security (optional but recommended)
SENTRY_DSN=your-sentry-dsn-here

# Static Files (for cloud storage)
# AWS_ACCESS_KEY_ID=your-aws-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret
# AWS_STORAGE_BUCKET_NAME=your-bucket-name
# AWS_S3_REGION_NAME=us-east-1
