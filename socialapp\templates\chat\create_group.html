{% extends 'base.html' %}
{% load static %}

{% block title %}Create Group Chat - SocialApp{% endblock %}

{% block extra_css %}
<style>
    .create-group-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .form-container {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        padding: 30px;
    }
    
    .form-group {
        margin-bottom: 25px;
    }
    
    .form-label {
        font-weight: 600;
        margin-bottom: 8px;
        display: block;
    }
    
    .form-input {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid #dee2e6;
        border-radius: 8px;
        font-size: 16px;
        outline: none;
        transition: border-color 0.2s;
    }
    
    .form-input:focus {
        border-color: #007bff;
    }
    
    .form-textarea {
        resize: vertical;
        min-height: 80px;
    }
    
    .participants-section {
        margin-top: 30px;
    }
    
    .user-list {
        max-height: 300px;
        overflow-y: auto;
        border: 2px solid #dee2e6;
        border-radius: 8px;
        padding: 10px;
    }
    
    .user-checkbox {
        display: flex;
        align-items: center;
        padding: 10px;
        border-radius: 6px;
        margin-bottom: 5px;
        cursor: pointer;
        transition: background-color 0.2s;
    }
    
    .user-checkbox:hover {
        background-color: #f8f9fa;
    }
    
    .user-checkbox input[type="checkbox"] {
        margin-right: 15px;
        transform: scale(1.2);
    }
    
    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #007bff;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        margin-right: 15px;
    }
    
    .user-info {
        flex: 1;
    }
    
    .user-name {
        font-weight: 500;
        margin-bottom: 2px;
    }
    
    .user-username {
        color: #6c757d;
        font-size: 14px;
    }
    
    .selected-count {
        color: #007bff;
        font-weight: 500;
        margin-top: 10px;
    }
    
    .btn-group {
        display: flex;
        gap: 15px;
        margin-top: 30px;
    }
    
    .btn-primary {
        background: #007bff;
        color: white;
        border: none;
        padding: 12px 30px;
        border-radius: 8px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;
    }
    
    .btn-primary:hover {
        background: #0056b3;
    }
    
    .btn-secondary {
        background: #6c757d;
        color: white;
        border: none;
        padding: 12px 30px;
        border-radius: 8px;
        font-weight: 500;
        text-decoration: none;
        display: inline-block;
        text-align: center;
        transition: background-color 0.2s;
    }
    
    .btn-secondary:hover {
        background: #545b62;
        color: white;
        text-decoration: none;
    }
    
    .back-link {
        color: #007bff;
        text-decoration: none;
        margin-bottom: 20px;
        display: inline-block;
    }
    
    .back-link:hover {
        text-decoration: underline;
    }
    
    .search-participants {
        margin-bottom: 15px;
    }
    
    .search-input {
        width: 100%;
        padding: 10px 15px;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        outline: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="create-group-container">
    <a href="{% url 'chat_home' %}" class="back-link">
        <i class="fas fa-arrow-left"></i> Back to Chats
    </a>
    
    <div class="form-container">
        <h3 class="mb-4">Create Group Chat</h3>
        
        <form method="post" action="{% url 'create_group_chat' %}">
            {% csrf_token %}
            
            <div class="form-group">
                <label for="name" class="form-label">Group Name *</label>
                <input 
                    type="text" 
                    id="name" 
                    name="name" 
                    class="form-input" 
                    placeholder="Enter group name..."
                    required
                    maxlength="100"
                >
            </div>
            
            <div class="form-group">
                <label for="description" class="form-label">Description (Optional)</label>
                <textarea 
                    id="description" 
                    name="description" 
                    class="form-input form-textarea" 
                    placeholder="Describe what this group is about..."
                    maxlength="500"
                ></textarea>
            </div>
            
            <div class="participants-section">
                <label class="form-label">Add Participants</label>
                
                <div class="search-participants">
                    <input 
                        type="text" 
                        id="search-users" 
                        class="search-input" 
                        placeholder="Search users..."
                    >
                </div>
                
                <div class="user-list" id="user-list">
                    {% for user in users %}
                        <label class="user-checkbox" data-username="{{ user.username|lower }}">
                            <input 
                                type="checkbox" 
                                name="participants" 
                                value="{{ user.username }}"
                                onchange="updateSelectedCount()"
                            >
                            <div class="user-avatar">
                                {{ user.username|first|upper }}
                            </div>
                            <div class="user-info">
                                <div class="user-name">
                                    {{ user.get_full_name|default:user.username }}
                                </div>
                                <div class="user-username">
                                    @{{ user.username }}
                                </div>
                            </div>
                        </label>
                    {% empty %}
                        <div class="text-center py-4 text-muted">
                            No users available to add to the group.
                        </div>
                    {% endfor %}
                </div>
                
                <div class="selected-count" id="selected-count">
                    0 participants selected
                </div>
            </div>
            
            <div class="btn-group">
                <button type="submit" class="btn-primary">
                    <i class="fas fa-plus"></i> Create Group
                </button>
                <a href="{% url 'chat_home' %}" class="btn-secondary">
                    Cancel
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Search functionality
    const searchInput = document.getElementById('search-users');
    const userList = document.getElementById('user-list');
    const userCheckboxes = userList.querySelectorAll('.user-checkbox');
    
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        
        userCheckboxes.forEach(function(checkbox) {
            const username = checkbox.dataset.username;
            const userInfo = checkbox.querySelector('.user-info').textContent.toLowerCase();
            
            if (username.includes(searchTerm) || userInfo.includes(searchTerm)) {
                checkbox.style.display = 'flex';
            } else {
                checkbox.style.display = 'none';
            }
        });
    });
    
    // Update selected count
    function updateSelectedCount() {
        const selectedCheckboxes = userList.querySelectorAll('input[type="checkbox"]:checked');
        const count = selectedCheckboxes.length;
        const selectedCountElement = document.getElementById('selected-count');
        
        if (count === 0) {
            selectedCountElement.textContent = '0 participants selected';
        } else if (count === 1) {
            selectedCountElement.textContent = '1 participant selected';
        } else {
            selectedCountElement.textContent = `${count} participants selected`;
        }
    }
    
    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const groupName = document.getElementById('name').value.trim();
        
        if (!groupName) {
            e.preventDefault();
            alert('Please enter a group name.');
            return;
        }
        
        const selectedParticipants = userList.querySelectorAll('input[type="checkbox"]:checked');
        if (selectedParticipants.length === 0) {
            const confirm = window.confirm('You haven\'t selected any participants. Create group with just yourself?');
            if (!confirm) {
                e.preventDefault();
            }
        }
    });
    
    // Initialize selected count
    updateSelectedCount();
</script>
{% endblock %}
