{% extends 'base.html' %}
{% load static %}

{% block title %}
    {% if chat_room.room_type == 'private' %}
        {% for participant in chat_room.participants.all %}
            {% if participant != request.user %}
                Chat with {{ participant.get_full_name|default:participant.username }}
            {% endif %}
        {% endfor %}
    {% else %}
        {{ chat_room.name }}
    {% endif %}
    - SocialApp
{% endblock %}

{% block extra_css %}
<style>
    .chat-container {
        height: calc(100vh - 120px);
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
    }
    
    .chat-header {
        background: white;
        padding: 20px;
        border-bottom: 1px solid #dee2e6;
        border-radius: 10px 10px 0 0;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .chat-header h4 {
        margin: 0;
        display: flex;
        align-items: center;
    }
    
    .chat-header .back-btn {
        margin-right: 15px;
        color: #007bff;
        text-decoration: none;
    }
    
    .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 20px;
        background: #f8f9fa;
        display: flex;
        flex-direction: column;
    }
    
    .message {
        margin-bottom: 15px;
        display: flex;
        align-items: flex-start;
    }
    
    .message.own {
        flex-direction: row-reverse;
    }
    
    .message-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #007bff;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        margin: 0 10px;
        flex-shrink: 0;
    }
    
    .message.own .message-avatar {
        background: #28a745;
    }
    
    .message-content {
        max-width: 70%;
        background: white;
        padding: 12px 16px;
        border-radius: 18px;
        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }
    
    .message.own .message-content {
        background: #007bff;
        color: white;
    }
    
    .message-text {
        margin: 0;
        word-wrap: break-word;
    }
    
    .message-meta {
        font-size: 0.75em;
        opacity: 0.7;
        margin-top: 5px;
    }
    
    .chat-input {
        background: white;
        padding: 20px;
        border-top: 1px solid #dee2e6;
        border-radius: 0 0 10px 10px;
    }
    
    .input-group {
        display: flex;
        align-items: center;
    }
    
    .message-input {
        flex: 1;
        border: 1px solid #dee2e6;
        border-radius: 25px;
        padding: 12px 20px;
        margin-right: 10px;
        outline: none;
        resize: none;
        max-height: 100px;
    }
    
    .send-btn {
        background: #007bff;
        color: white;
        border: none;
        border-radius: 50%;
        width: 45px;
        height: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: background-color 0.2s;
    }
    
    .send-btn:hover {
        background: #0056b3;
    }
    
    .send-btn:disabled {
        background: #6c757d;
        cursor: not-allowed;
    }
    
    .typing-indicator {
        padding: 10px 20px;
        font-style: italic;
        color: #6c757d;
        font-size: 0.9em;
    }
    
    .participants-info {
        font-size: 0.9em;
        color: #6c757d;
        margin-top: 5px;
    }
    
    .online-status {
        width: 8px;
        height: 8px;
        background: #28a745;
        border-radius: 50%;
        display: inline-block;
        margin-left: 5px;
    }
    
    .system-message {
        text-align: center;
        color: #6c757d;
        font-style: italic;
        margin: 10px 0;
        font-size: 0.9em;
    }
</style>
{% endblock %}

{% block content %}
<div class="chat-container">
    <div class="chat-header">
        <h4>
            <a href="{% url 'chat_home' %}" class="back-btn">
                <i class="fas fa-arrow-left"></i>
            </a>
            {% if chat_room.room_type == 'private' %}
                {% for participant in chat_room.participants.all %}
                    {% if participant != request.user %}
                        {{ participant.get_full_name|default:participant.username }}
                        <span class="online-status"></span>
                    {% endif %}
                {% endfor %}
            {% else %}
                {{ chat_room.name }}
            {% endif %}
        </h4>
        {% if chat_room.room_type == 'group' %}
            <div class="participants-info">
                {{ other_participants.count|add:1 }} participants
            </div>
        {% endif %}
    </div>
    
    <div class="chat-messages" id="chat-messages">
        {% for message in messages %}
            <div class="message {% if message.sender == request.user %}own{% endif %}">
                <div class="message-avatar">
                    {{ message.sender.username|first|upper }}
                </div>
                <div class="message-content">
                    <div class="message-text">{{ message.content }}</div>
                    <div class="message-meta">
                        {{ message.created_at|date:"H:i" }}
                        {% if message.is_edited %}(edited){% endif %}
                    </div>
                </div>
            </div>
        {% empty %}
            <div class="system-message">
                No messages yet. Start the conversation!
            </div>
        {% endfor %}
    </div>
    
    <div class="typing-indicator" id="typing-indicator" style="display: none;">
        <span id="typing-users"></span> is typing...
    </div>
    
    <div class="chat-input">
        <div class="input-group">
            <textarea 
                id="message-input" 
                class="message-input" 
                placeholder="Type a message..." 
                rows="1"
                maxlength="1000"
            ></textarea>
            <button id="send-btn" class="send-btn" type="button">
                <i class="fas fa-paper-plane"></i>
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    const roomId = '{{ room_id }}';
    const currentUser = '{{ request.user.username }}';
    const currentUserId = {{ request.user.id }};
    
    // WebSocket connection
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws/chat/${roomId}/`;
    const chatSocket = new WebSocket(wsUrl);
    
    const messagesContainer = document.getElementById('chat-messages');
    const messageInput = document.getElementById('message-input');
    const sendBtn = document.getElementById('send-btn');
    const typingIndicator = document.getElementById('typing-indicator');
    const typingUsers = document.getElementById('typing-users');
    
    let typingTimer;
    let isTyping = false;
    
    // WebSocket event handlers
    chatSocket.onmessage = function(e) {
        const data = JSON.parse(e.data);
        
        if (data.type === 'message') {
            addMessage(data.message);
        } else if (data.type === 'typing') {
            handleTypingIndicator(data);
        } else if (data.type === 'user_join') {
            addSystemMessage(`${data.user} joined the chat`);
        } else if (data.type === 'user_leave') {
            addSystemMessage(`${data.user} left the chat`);
        }
    };
    
    chatSocket.onclose = function(e) {
        console.error('Chat socket closed unexpectedly');
        // Attempt to reconnect after 3 seconds
        setTimeout(function() {
            location.reload();
        }, 3000);
    };
    
    // Send message function
    function sendMessage() {
        const message = messageInput.value.trim();
        if (message) {
            chatSocket.send(JSON.stringify({
                'type': 'message',
                'content': message
            }));
            messageInput.value = '';
            adjustTextareaHeight();
            stopTyping();
        }
    }
    
    // Add message to chat
    function addMessage(message) {
        const isOwn = message.sender === currentUser;
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${isOwn ? 'own' : ''}`;
        
        const time = new Date(message.created_at).toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
        });
        
        messageDiv.innerHTML = `
            <div class="message-avatar">
                ${message.sender.charAt(0).toUpperCase()}
            </div>
            <div class="message-content">
                <div class="message-text">${escapeHtml(message.content)}</div>
                <div class="message-meta">${time}</div>
            </div>
        `;
        
        messagesContainer.appendChild(messageDiv);
        scrollToBottom();
    }
    
    // Add system message
    function addSystemMessage(text) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'system-message';
        messageDiv.textContent = text;
        messagesContainer.appendChild(messageDiv);
        scrollToBottom();
    }
    
    // Handle typing indicator
    function handleTypingIndicator(data) {
        if (data.is_typing) {
            typingUsers.textContent = data.user;
            typingIndicator.style.display = 'block';
        } else {
            typingIndicator.style.display = 'none';
        }
    }
    
    // Typing functions
    function startTyping() {
        if (!isTyping) {
            isTyping = true;
            chatSocket.send(JSON.stringify({
                'type': 'typing',
                'is_typing': true
            }));
        }
        
        clearTimeout(typingTimer);
        typingTimer = setTimeout(stopTyping, 3000);
    }
    
    function stopTyping() {
        if (isTyping) {
            isTyping = false;
            chatSocket.send(JSON.stringify({
                'type': 'typing',
                'is_typing': false
            }));
        }
        clearTimeout(typingTimer);
    }
    
    // Utility functions
    function scrollToBottom() {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    function adjustTextareaHeight() {
        messageInput.style.height = 'auto';
        messageInput.style.height = Math.min(messageInput.scrollHeight, 100) + 'px';
    }
    
    // Event listeners
    sendBtn.addEventListener('click', sendMessage);
    
    messageInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        } else {
            startTyping();
        }
    });
    
    messageInput.addEventListener('input', function() {
        adjustTextareaHeight();
        startTyping();
    });
    
    // Initial setup
    scrollToBottom();
    messageInput.focus();
    
    // Send read receipt
    chatSocket.send(JSON.stringify({
        'type': 'read_receipt'
    }));
</script>
{% endblock %}
