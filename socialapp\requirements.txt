# Django SocialApp Requirements
# Core Django
Django>=5.2.3,<5.3
django-environ>=0.11.2

# Database
psycopg2-binary>=2.9.9  # PostgreSQL adapter
mysqlclient>=2.2.0      # MySQL adapter

# Image Processing
Pillow>=10.1.0          # Image processing
django-imagekit>=5.0.0  # Image manipulation

# Security
django-cors-headers>=4.3.1
django-ratelimit>=4.1.0

# Real-time Features
channels>=4.0.0
channels-redis>=4.1.0

# Development & Debugging
django-debug-toolbar>=4.2.0
django-extensions>=3.2.3

# Testing
pytest>=7.4.3
pytest-django>=4.7.0
factory-boy>=3.3.0
coverage>=7.3.2

# Code Quality
black>=23.11.0
flake8>=6.1.0
isort>=5.12.0
pre-commit>=3.5.0

# Documentation
Sphinx>=7.2.6
sphinx-rtd-theme>=1.3.0

# Production
gunicorn>=21.2.0
whitenoise>=6.6.0
redis>=5.0.1
celery>=5.3.4

# Monitoring
sentry-sdk>=1.38.0
django-prometheus>=2.3.1 