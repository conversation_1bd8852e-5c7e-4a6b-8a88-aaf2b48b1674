{% extends 'base.html' %}

{% block title %}Login - BlogApp{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card">
            <div class="card-header text-center">
                <h4 class="mb-0"><i class="fas fa-sign-in-alt"></i> Login</h4>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    {% if form.errors %}
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> 
                        Please correct the errors below.
                    </div>
                    {% endif %}
                    
                    <div class="mb-3">
                        <label for="{{ form.username.id_for_label }}" class="form-label">
                            <i class="fas fa-user"></i> Username
                        </label>
                        <input type="text" 
                               class="form-control {% if form.username.errors %}is-invalid{% endif %}" 
                               id="{{ form.username.id_for_label }}" 
                               name="{{ form.username.name }}" 
                               value="{{ form.username.value|default:'' }}"
                               required>
                        {% if form.username.errors %}
                        <div class="invalid-feedback">
                            {{ form.username.errors.0 }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.password.id_for_label }}" class="form-label">
                            <i class="fas fa-lock"></i> Password
                        </label>
                        <input type="password" 
                               class="form-control {% if form.password.errors %}is-invalid{% endif %}" 
                               id="{{ form.password.id_for_label }}" 
                               name="{{ form.password.name }}" 
                               required>
                        {% if form.password.errors %}
                        <div class="invalid-feedback">
                            {{ form.password.errors.0 }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt"></i> Login
                        </button>
                    </div>
                </form>
                
                <hr class="my-4">
                
                <div class="text-center">
                    <p class="text-muted mb-2">Don't have an account?</p>
                    <a href="{% url 'register' %}" class="btn btn-outline-primary">
                        <i class="fas fa-user-plus"></i> Sign Up
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Help Section -->
        <div class="card mt-3">
            <div class="card-body">
                <h6 class="card-title"><i class="fas fa-question-circle"></i> Need Help?</h6>
                <ul class="list-unstyled mb-0">
                    <li><i class="fas fa-info-circle text-info me-2"></i>Make sure your username and password are correct</li>
                    <li><i class="fas fa-key text-warning me-2"></i>Passwords are case-sensitive</li>
                    <li><i class="fas fa-envelope text-success me-2"></i>Contact support if you can't access your account</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
