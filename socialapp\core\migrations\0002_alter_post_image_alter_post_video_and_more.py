# Generated by Django 5.2.3 on 2025-06-29 11:24

import core.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name="post",
            name="image",
            field=models.ImageField(
                blank=True,
                help_text="Image attached to the post (max 10MB)",
                null=True,
                upload_to="posts/%Y/%m/%d/",
                validators=[core.models.validate_image_file],
            ),
        ),
        migrations.AlterField(
            model_name="post",
            name="video",
            field=models.FileField(
                blank=True,
                help_text="Video attached to the post (max 100MB)",
                null=True,
                upload_to="posts/videos/%Y/%m/%d/",
                validators=[core.models.validate_video_file],
            ),
        ),
        migrations.AlterField(
            model_name="profile",
            name="avatar",
            field=models.ImageField(
                blank=True,
                help_text="User's profile picture (max 5MB)",
                null=True,
                upload_to="avatars/%Y/%m/%d/",
                validators=[core.models.validate_image_file],
            ),
        ),
        migrations.AlterField(
            model_name="profile",
            name="cover_photo",
            field=models.ImageField(
                blank=True,
                help_text="User's cover photo (max 5MB)",
                null=True,
                upload_to="covers/%Y/%m/%d/",
                validators=[core.models.validate_image_file],
            ),
        ),
        migrations.AddConstraint(
            model_name="follow",
            constraint=models.CheckConstraint(
                condition=models.Q(("follower", models.F("following")), _negated=True),
                name="prevent_self_follow",
            ),
        ),
    ]
