# Generated by Django 5.2.3 on 2025-06-29 10:09

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Comment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                (
                    "content",
                    models.TextField(
                        help_text="The content of the comment",
                        validators=[
                            django.core.validators.MinLengthValidator(
                                1, message="Comment cannot be empty."
                            ),
                            django.core.validators.MaxLengthValidator(
                                1000, message="Comment cannot exceed 1000 characters."
                            ),
                        ],
                    ),
                ),
                (
                    "is_edited",
                    models.<PERSON><PERSON>anField(
                        default=False, help_text="Whether this comment has been edited"
                    ),
                ),
                (
                    "edited_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When the comment was last edited",
                        null=True,
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(
                        default=False, help_text="Whether this comment has been deleted"
                    ),
                ),
                (
                    "likes_count",
                    models.PositiveIntegerField(
                        default=0, help_text="Number of likes on this comment"
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        help_text="Parent comment if this is a reply",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="replies",
                        to="core.comment",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        help_text="User who wrote the comment",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="comments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Comment",
                "verbose_name_plural": "Comments",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Hashtag",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                (
                    "name",
                    models.CharField(
                        help_text="Hashtag name (without #)", max_length=50, unique=True
                    ),
                ),
                (
                    "slug",
                    models.SlugField(
                        help_text="URL-friendly version of the hashtag", unique=True
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Description of the hashtag",
                        max_length=500,
                    ),
                ),
                (
                    "posts_count",
                    models.PositiveIntegerField(
                        default=0, help_text="Number of posts using this hashtag"
                    ),
                ),
            ],
            options={
                "verbose_name": "Hashtag",
                "verbose_name_plural": "Hashtags",
                "ordering": ["-posts_count", "-created_at"],
                "indexes": [
                    models.Index(fields=["slug"], name="core_hashta_slug_155ef9_idx"),
                    models.Index(fields=["name"], name="core_hashta_name_9667f8_idx"),
                ],
            },
        ),
        migrations.CreateModel(
            name="Post",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                (
                    "content",
                    models.TextField(
                        help_text="The content of the post",
                        validators=[
                            django.core.validators.MinLengthValidator(
                                1, message="Post content cannot be empty."
                            ),
                            django.core.validators.MaxLengthValidator(
                                10000, message="Post cannot exceed 10000 characters."
                            ),
                        ],
                    ),
                ),
                (
                    "image",
                    models.ImageField(
                        blank=True,
                        help_text="Image attached to the post",
                        null=True,
                        upload_to="posts/%Y/%m/%d/",
                    ),
                ),
                (
                    "video",
                    models.FileField(
                        blank=True,
                        help_text="Video attached to the post",
                        null=True,
                        upload_to="posts/videos/%Y/%m/%d/",
                    ),
                ),
                (
                    "is_public",
                    models.BooleanField(
                        default=True, help_text="Whether the post is public or private"
                    ),
                ),
                (
                    "is_edited",
                    models.BooleanField(
                        default=False, help_text="Whether this post has been edited"
                    ),
                ),
                (
                    "edited_at",
                    models.DateTimeField(
                        blank=True, help_text="When the post was last edited", null=True
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(
                        default=False, help_text="Whether this post has been deleted"
                    ),
                ),
                (
                    "likes_count",
                    models.PositiveIntegerField(
                        default=0, help_text="Number of likes on this post"
                    ),
                ),
                (
                    "comments_count",
                    models.PositiveIntegerField(
                        default=0, help_text="Number of comments on this post"
                    ),
                ),
                (
                    "shares_count",
                    models.PositiveIntegerField(
                        default=0, help_text="Number of times this post has been shared"
                    ),
                ),
                (
                    "views_count",
                    models.PositiveIntegerField(
                        default=0, help_text="Number of views on this post"
                    ),
                ),
                (
                    "hashtags",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Hashtags associated with this post",
                        related_name="posts",
                        to="core.hashtag",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        help_text="User who created the post",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="posts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Post",
                "verbose_name_plural": "Posts",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Notification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                (
                    "notification_type",
                    models.CharField(
                        choices=[
                            ("follow", "Follow"),
                            ("like", "Like"),
                            ("comment", "Comment"),
                            ("share", "Share"),
                            ("mention", "Mention"),
                        ],
                        help_text="Type of notification",
                        max_length=20,
                    ),
                ),
                (
                    "is_read",
                    models.BooleanField(
                        default=False,
                        help_text="Whether the notification has been read",
                    ),
                ),
                (
                    "comment",
                    models.ForeignKey(
                        blank=True,
                        help_text="Related comment",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notifications",
                        to="core.comment",
                    ),
                ),
                (
                    "recipient",
                    models.ForeignKey(
                        help_text="User receiving the notification",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notifications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "sender",
                    models.ForeignKey(
                        help_text="User who triggered the notification",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sent_notifications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "post",
                    models.ForeignKey(
                        blank=True,
                        help_text="Related post",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notifications",
                        to="core.post",
                    ),
                ),
            ],
            options={
                "verbose_name": "Notification",
                "verbose_name_plural": "Notifications",
                "ordering": ["-created_at"],
            },
        ),
        migrations.AddField(
            model_name="comment",
            name="post",
            field=models.ForeignKey(
                help_text="Post this comment belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="comments",
                to="core.post",
            ),
        ),
        migrations.CreateModel(
            name="Bookmark",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                (
                    "user",
                    models.ForeignKey(
                        help_text="User who bookmarked",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bookmarks",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "post",
                    models.ForeignKey(
                        help_text="Post being bookmarked",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bookmarks",
                        to="core.post",
                    ),
                ),
            ],
            options={
                "verbose_name": "Bookmark",
                "verbose_name_plural": "Bookmarks",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Profile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                (
                    "bio",
                    models.TextField(
                        blank=True,
                        help_text="User's biography (max 500 characters)",
                        max_length=500,
                        validators=[
                            django.core.validators.MinLengthValidator(
                                10, message="Bio must be at least 10 characters long."
                            ),
                            django.core.validators.MaxLengthValidator(
                                500, message="Bio cannot exceed 500 characters."
                            ),
                        ],
                    ),
                ),
                (
                    "avatar",
                    models.ImageField(
                        blank=True,
                        help_text="User's profile picture",
                        null=True,
                        upload_to="avatars/%Y/%m/%d/",
                    ),
                ),
                (
                    "cover_photo",
                    models.ImageField(
                        blank=True,
                        help_text="User's cover photo",
                        null=True,
                        upload_to="covers/%Y/%m/%d/",
                    ),
                ),
                (
                    "date_of_birth",
                    models.DateField(
                        blank=True, help_text="User's date of birth", null=True
                    ),
                ),
                (
                    "location",
                    models.CharField(
                        blank=True, help_text="User's location", max_length=100
                    ),
                ),
                (
                    "website",
                    models.URLField(blank=True, help_text="User's personal website"),
                ),
                (
                    "is_verified",
                    models.BooleanField(
                        default=False, help_text="Whether the user account is verified"
                    ),
                ),
                (
                    "followers_count",
                    models.PositiveIntegerField(
                        default=0, help_text="Number of followers"
                    ),
                ),
                (
                    "following_count",
                    models.PositiveIntegerField(
                        default=0, help_text="Number of users being followed"
                    ),
                ),
                (
                    "posts_count",
                    models.PositiveIntegerField(
                        default=0, help_text="Number of posts by this user"
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        help_text="The user this profile belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="profile",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Profile",
                "verbose_name_plural": "User Profiles",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Reaction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                (
                    "reaction_type",
                    models.CharField(
                        choices=[
                            ("like", "Like"),
                            ("love", "Love"),
                            ("laugh", "Laugh"),
                            ("wow", "Wow"),
                            ("sad", "Sad"),
                            ("angry", "Angry"),
                        ],
                        default="like",
                        help_text="Type of reaction",
                        max_length=10,
                    ),
                ),
                (
                    "comment",
                    models.ForeignKey(
                        blank=True,
                        help_text="Comment being reacted to",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reactions",
                        to="core.comment",
                    ),
                ),
                (
                    "post",
                    models.ForeignKey(
                        blank=True,
                        help_text="Post being reacted to",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reactions",
                        to="core.post",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        help_text="User who reacted",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reactions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Reaction",
                "verbose_name_plural": "Reactions",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Share",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                (
                    "share_text",
                    models.TextField(
                        blank=True,
                        help_text="Additional text when sharing",
                        max_length=1000,
                    ),
                ),
                (
                    "original_post",
                    models.ForeignKey(
                        help_text="Original post being shared",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="shares",
                        to="core.post",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        help_text="User who shared",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="shares",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Share",
                "verbose_name_plural": "Shares",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Follow",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                (
                    "follower",
                    models.ForeignKey(
                        help_text="User who is following",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="following",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "following",
                    models.ForeignKey(
                        help_text="User being followed",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="followers",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Follow",
                "verbose_name_plural": "Follows",
                "ordering": ["-created_at"],
                "unique_together": {("follower", "following")},
            },
        ),
        migrations.AddIndex(
            model_name="post",
            index=models.Index(
                fields=["user", "-created_at"], name="core_post_user_id_5973f4_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="post",
            index=models.Index(
                fields=["is_public", "-created_at"], name="core_post_is_publ_85759d_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="post",
            index=models.Index(
                fields=["is_deleted", "-created_at"],
                name="core_post_is_dele_816102_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="notification",
            index=models.Index(
                fields=["recipient", "is_read"], name="core_notifi_recipie_aeffaf_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="comment",
            index=models.Index(
                fields=["post", "-created_at"], name="core_commen_post_id_7e3d35_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="comment",
            index=models.Index(
                fields=["user", "-created_at"], name="core_commen_user_id_315d2e_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="comment",
            index=models.Index(
                fields=["parent", "-created_at"], name="core_commen_parent__40b81d_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="bookmark",
            unique_together={("user", "post")},
        ),
        migrations.AlterUniqueTogether(
            name="reaction",
            unique_together={("user", "comment"), ("user", "post")},
        ),
    ]
