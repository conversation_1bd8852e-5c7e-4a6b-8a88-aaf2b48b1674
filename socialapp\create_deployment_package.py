#!/usr/bin/env python3
"""
Create deployment package for Django SocialApp
Prepares a clean package ready for upload to hosting server
"""

import os
import shutil
import zipfile
from pathlib import Path

def create_deployment_package():
    """Create a clean deployment package"""
    print("📦 Creating deployment package...")
    
    # Define source and destination
    source_dir = Path('.')
    package_dir = Path('deployment_package')
    zip_file = Path('socialapp_deployment.zip')
    
    # Remove existing package directory
    if package_dir.exists():
        shutil.rmtree(package_dir)
    
    # Create package directory
    package_dir.mkdir()
    
    # Files and directories to include
    include_patterns = [
        '*.py',
        '*.txt',
        '*.md',
        '*.json',
        '*.conf',
        '*.sh',
        '*.env',
        'socialapp/',
        'core/',
        'templates/',
        'static/',
        'media/',
        'staticfiles/',
    ]
    
    # Files and directories to exclude
    exclude_patterns = [
        '__pycache__/',
        '*.pyc',
        '.git/',
        '.vscode/',
        '.idea/',
        'venv/',
        'env/',
        '.env.example',
        'db.sqlite3',
        '*.log',
        'deployment_package/',
        'socialapp_deployment.zip',
        'create_deployment_package.py',
        'deploy_to_hosting.py',
    ]
    
    def should_include(path):
        """Check if a file/directory should be included"""
        path_str = str(path)
        
        # Check exclude patterns
        for pattern in exclude_patterns:
            if pattern in path_str or path_str.endswith(pattern.rstrip('/')):
                return False
        
        # Check include patterns
        for pattern in include_patterns:
            if pattern.endswith('/'):
                if path.is_dir() and (pattern.rstrip('/') in path_str or path_str.startswith(pattern.rstrip('/'))):
                    return True
            else:
                if path.is_file() and (path_str.endswith(pattern.lstrip('*')) or pattern == '*'):
                    return True
        
        return False
    
    # Copy files
    copied_files = 0
    for item in source_dir.rglob('*'):
        if should_include(item):
            # Calculate relative path
            rel_path = item.relative_to(source_dir)
            dest_path = package_dir / rel_path
            
            try:
                if item.is_file():
                    # Create parent directories if they don't exist
                    dest_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(item, dest_path)
                    copied_files += 1
                elif item.is_dir():
                    dest_path.mkdir(parents=True, exist_ok=True)
            except Exception as e:
                print(f"⚠️  Warning: Could not copy {item}: {e}")
    
    print(f"✅ Copied {copied_files} files to deployment package")
    
    # Create deployment info file
    info_content = f"""# Django SocialApp Deployment Package
Generated for: bloggingapp.great-site.net
Date: {os.popen('date').read().strip()}

## Package Contents:
- Django application files
- Production configuration
- Static files
- Templates
- Requirements file
- Deployment scripts

## Next Steps:
1. Upload all files to your hosting server
2. Follow instructions in UPLOAD_INSTRUCTIONS.md
3. Install requirements: pip install -r requirements-production.txt
4. Run migrations: python manage.py migrate
5. Collect static files: python manage.py collectstatic
6. Start application: gunicorn socialapp.wsgi:application

## Important Files:
- .env: Production environment variables
- requirements-production.txt: Python dependencies
- gunicorn.conf.py: Production server configuration
- UPLOAD_INSTRUCTIONS.md: Detailed deployment guide
"""
    
    with open(package_dir / 'DEPLOYMENT_INFO.txt', 'w') as f:
        f.write(info_content)
    
    # Create ZIP file
    print("🗜️  Creating ZIP file...")
    with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in package_dir.rglob('*'):
            if file_path.is_file():
                arcname = file_path.relative_to(package_dir)
                zipf.write(file_path, arcname)
    
    # Get file sizes
    package_size = sum(f.stat().st_size for f in package_dir.rglob('*') if f.is_file())
    zip_size = zip_file.stat().st_size
    
    print(f"✅ Created deployment package:")
    print(f"   📁 Folder: {package_dir} ({package_size / 1024 / 1024:.1f} MB)")
    print(f"   📦 ZIP: {zip_file} ({zip_size / 1024 / 1024:.1f} MB)")
    
    print("\n🚀 Ready for deployment!")
    print(f"📤 Upload either:")
    print(f"   1. ZIP file: {zip_file}")
    print(f"   2. Folder contents: {package_dir}/")
    print(f"\n📖 Follow instructions in: UPLOAD_INSTRUCTIONS.md")

if __name__ == '__main__':
    create_deployment_package()
