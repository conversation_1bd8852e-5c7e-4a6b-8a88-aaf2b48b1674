{% extends 'base.html' %}
{% load static %}

{% block title %}Rate Limit Exceeded{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle"></i>
                        Rate Limit Exceeded
                    </h4>
                </div>
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-clock fa-3x text-warning"></i>
                    </div>
                    
                    <h5 class="card-title">Too Many Requests</h5>
                    <p class="card-text">
                        {{ message|default:"You've made too many requests. Please slow down and try again later." }}
                    </p>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        Please wait <strong>{{ retry_after|default:60 }} seconds</strong> before trying again.
                    </div>
                    
                    <div class="mt-4">
                        <a href="{% url 'home' %}" class="btn btn-primary">
                            <i class="fas fa-home"></i>
                            Return to Home
                        </a>
                        <button onclick="history.back()" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            Go Back
                        </button>
                    </div>
                </div>
                <div class="card-footer text-muted text-center">
                    <small>
                        This limit helps protect our service and ensures fair usage for all users.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-redirect after retry period
setTimeout(function() {
    window.location.href = "{% url 'home' %}";
}, {{ retry_after|default:60 }} * 1000);

// Countdown timer
let timeLeft = {{ retry_after|default:60 }};
const countdownElement = document.querySelector('strong');
const countdown = setInterval(function() {
    timeLeft--;
    if (countdownElement) {
        countdownElement.textContent = timeLeft + ' seconds';
    }
    if (timeLeft <= 0) {
        clearInterval(countdown);
        window.location.reload();
    }
}, 1000);
</script>
{% endblock %}
