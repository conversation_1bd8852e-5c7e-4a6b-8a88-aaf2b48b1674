{% extends "base.html" %}
{% block content %}
<div class="mb-4">
    <h2>{{ group.name }}</h2>
    <p>{{ group.description }}</p>
    <p class="text-muted">Created by {{ group.creator.username }} on {{ group.created_at }}</p>
</div>

{% if user == group.creator %}
<div class="mb-3">
    <a href="{% url 'group_edit' group.slug %}" class="btn btn-warning btn-sm">Edit</a>
    <a href="{% url 'group_delete' group.slug %}" class="btn btn-danger btn-sm">Delete</a>
</div>
{% endif %}

<hr>
<h4>Opinions</h4>

<form method="post" action="{% url 'post_opinion' group.slug %}" class="mb-3">
    {% csrf_token %}
    <textarea name="content" class="form-control mb-2" placeholder="Write your opinion..." required></textarea>
    <button type="submit" class="btn btn-primary">Post</button>
</form>

{% for opinion in group.opinions.filter(parent__isnull=True).order_by('-created_at') %}
    <div class="card mb-3">
        <div class="card-body">
            <strong>{{ opinion.user.username }}</strong><br>
            <p>{{ opinion.content }}</p>

            <form method="post" action="{% url 'toggle_like' opinion.id %}" class="d-inline">
                {% csrf_token %}
                <button type="submit" class="btn btn-outline-danger btn-sm">❤️ {{ opinion.likes.count }}</button>
            </form>

            <form method="post" action="{% url 'post_opinion' group.slug %}" class="mt-2">
                {% csrf_token %}
                <input type="hidden" name="parent" value="{{ opinion.id }}">
                <textarea name="content" class="form-control mb-2" placeholder="Reply..." required></textarea>
                <button type="submit" class="btn btn-sm btn-secondary">Reply</button>
            </form>

            {% for reply in opinion.replies.all %}
                <div class="ms-4 mt-3 border-start ps-3">
                    <strong>{{ reply.user.username }}</strong><br>
                    <p>{{ reply.content }}</p>
                    <form method="post" action="{% url 'toggle_like' reply.id %}" class="d-inline">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-outline-danger btn-sm">❤️ {{ reply.likes.count }}</button>
                    </form>
                </div>
            {% endfor %}
        </div>
    </div>
{% empty %}
    <p>No opinions yet.</p>
{% endfor %}
{% endblock %}
