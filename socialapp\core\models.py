# type: ignore
"""
Core models for the blogging platform.

This module contains the main data models for the social blogging application,
including User profiles, Posts, Comments, Reactions, Hashtags, and social features.
"""

from django.contrib.auth.models import User
from django.db import models
from django.utils.text import slugify
from django.contrib.auth.models import Group as AuthGroup
from django.core.validators import MinLengthValidator, MaxLengthValidator
from django.urls import reverse
from django.utils import timezone
from django.conf import settings
import re


class TimeStampedModel(models.Model):
    """Abstract base model that provides self-updating created_at and updated_at fields."""
    
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True, db_index=True)

    class Meta:
        abstract = True


class Profile(TimeStampedModel):
    """
    Extended user profile model with social blogging features.
    """
    
    user = models.OneToOneField(
        User, 
        on_delete=models.CASCADE, 
        related_name='profile',
        help_text="The user this profile belongs to"
    )
    bio = models.TextField(
        blank=True, 
        max_length=500,
        validators=[
            MinLengthValidator(10, message="Bio must be at least 10 characters long."),
            MaxLengthValidator(500, message="Bio cannot exceed 500 characters.")
        ],
        help_text="User's biography (max 500 characters)"
    )
    avatar = models.ImageField(
        upload_to='avatars/%Y/%m/%d/', 
        blank=True, 
        null=True,
        help_text="User's profile picture"
    )
    cover_photo = models.ImageField(
        upload_to='covers/%Y/%m/%d/',
        blank=True,
        null=True,
        help_text="User's cover photo"
    )
    date_of_birth = models.DateField(
        blank=True, 
        null=True,
        help_text="User's date of birth"
    )
    location = models.CharField(
        max_length=100, 
        blank=True,
        help_text="User's location"
    )
    website = models.URLField(
        blank=True,
        help_text="User's personal website"
    )
    is_verified = models.BooleanField(
        default=False,
        help_text="Whether the user account is verified"
    )
    followers_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of followers"
    )
    following_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of users being followed"
    )
    posts_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of posts by this user"
    )

    class Meta:
        verbose_name = "User Profile"
        verbose_name_plural = "User Profiles"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username}'s Profile"

    def get_absolute_url(self):
        """Return the URL to access a particular profile."""
        return reverse('profile', kwargs={'username': self.user.username})

    def get_avatar_url(self):
        """Return the avatar URL or a default avatar."""
        if self.avatar:
            return self.avatar.url
        return '/static/images/default-avatar.png'

    def get_cover_url(self):
        """Return the cover photo URL or a default cover."""
        if self.cover_photo:
            return self.cover_photo.url
        return '/static/images/default-cover.jpg'

    def update_counts(self):
        """Update follower, following, and post counts."""
        self.followers_count = self.followers.count()
        self.following_count = self.following.count()
        self.posts_count = self.user.posts.filter(is_deleted=False).count()
        self.save(update_fields=['followers_count', 'following_count', 'posts_count'])


class Follow(TimeStampedModel):
    """
    Follow relationship between users.
    """
    
    follower = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='following',
        help_text="User who is following"
    )
    following = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='followers',
        help_text="User being followed"
    )

    class Meta:
        unique_together = ('follower', 'following')
        verbose_name = "Follow"
        verbose_name_plural = "Follows"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.follower.username} follows {self.following.username}"


class Hashtag(TimeStampedModel):
    """
    Hashtag model for categorizing posts.
    """
    
    name = models.CharField(
        max_length=50,
        unique=True,
        help_text="Hashtag name (without #)"
    )
    slug = models.SlugField(
        unique=True,
        max_length=50,
        help_text="URL-friendly version of the hashtag"
    )
    description = models.TextField(
        blank=True,
        max_length=500,
        help_text="Description of the hashtag"
    )
    posts_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of posts using this hashtag"
    )

    class Meta:
        verbose_name = "Hashtag"
        verbose_name_plural = "Hashtags"
        ordering = ['-posts_count', '-created_at']
        indexes = [
            models.Index(fields=['slug']),
            models.Index(fields=['name']),
        ]

    def __str__(self):
        return f"#{self.name}"

    def save(self, *args, **kwargs):
        """Generate slug if not provided."""
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        """Return the URL to access this hashtag."""
        return reverse('hashtag_detail', kwargs={'slug': self.slug})

    def update_posts_count(self):
        """Update the posts count."""
        self.posts_count = self.posts.filter(is_deleted=False).count()
        self.save(update_fields=['posts_count'])


class Post(TimeStampedModel):
    """
    Post model for user-generated content.
    
    Represents the main content unit in the blogging platform.
    """
    
    user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE,
        related_name='posts',
        help_text="User who created the post"
    )
    content = models.TextField(
        validators=[
            MinLengthValidator(1, message="Post content cannot be empty."),
            MaxLengthValidator(10000, message="Post cannot exceed 10000 characters.")
        ],
        help_text="The content of the post"
    )
    image = models.ImageField(
        upload_to='posts/%Y/%m/%d/',
        blank=True,
        null=True,
        help_text="Image attached to the post"
    )
    video = models.FileField(
        upload_to='posts/videos/%Y/%m/%d/',
        blank=True,
        null=True,
        help_text="Video attached to the post"
    )
    hashtags = models.ManyToManyField(
        Hashtag,
        related_name='posts',
        blank=True,
        help_text="Hashtags associated with this post"
    )
    is_public = models.BooleanField(
        default=True,
        help_text="Whether the post is public or private"
    )
    is_edited = models.BooleanField(
        default=False,
        help_text="Whether this post has been edited"
    )
    edited_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the post was last edited"
    )
    is_deleted = models.BooleanField(
        default=False,
        help_text="Whether this post has been deleted"
    )
    likes_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of likes on this post"
    )
    comments_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of comments on this post"
    )
    shares_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of times this post has been shared"
    )
    views_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of views on this post"
    )

    class Meta:
        verbose_name = "Post"
        verbose_name_plural = "Posts"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['is_public', '-created_at']),
            models.Index(fields=['is_deleted', '-created_at']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.content[:50]}..."

    def save(self, *args, **kwargs):
        """Handle editing timestamp and extract hashtags."""
        if self.pk:  # If this is an update
            self.is_edited = True
            self.edited_at = timezone.now()
        
        # Extract hashtags from content
        hashtag_pattern = r'#(\w+)'
        hashtags_in_content = re.findall(hashtag_pattern, self.content)
        
        super().save(*args, **kwargs)
        
        # Add hashtags to the post
        for hashtag_name in hashtags_in_content:
            hashtag, created = Hashtag.objects.get_or_create(
                name=hashtag_name.lower(),
                defaults={'description': f'Posts tagged with #{hashtag_name}'}
            )
            self.hashtags.add(hashtag)

    def get_absolute_url(self):
        """Return the URL to access this post."""
        return reverse('post_detail', kwargs={'pk': self.pk})

    def update_counts(self):
        """Update like, comment, and share counts."""
        self.likes_count = self.likes.count()
        self.comments_count = self.comments.filter(is_deleted=False).count()
        self.shares_count = self.shares.count()
        self.save(update_fields=['likes_count', 'comments_count', 'shares_count'])

    def is_liked_by(self, user):
        """Check if this post is liked by a specific user."""
        return self.likes.filter(user=user).exists()

    def is_bookmarked_by(self, user):
        """Check if this post is bookmarked by a specific user."""
        return self.bookmarks.filter(user=user).exists()

    def soft_delete(self):
        """Soft delete the post."""
        self.is_deleted = True
        self.save(update_fields=['is_deleted'])

    def get_hashtags_display(self):
        """Return hashtags as a formatted string."""
        return ' '.join([f'#{hashtag.name}' for hashtag in self.hashtags.all()])


class Comment(TimeStampedModel):
    """
    Comment model for post interactions.
    
    Represents comments on posts with nested replies.
    """
    
    post = models.ForeignKey(
        Post, 
        on_delete=models.CASCADE, 
        related_name='comments',
        help_text="Post this comment belongs to"
    )
    user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE,
        related_name='comments',
        help_text="User who wrote the comment"
    )
    content = models.TextField(
        validators=[
            MinLengthValidator(1, message="Comment cannot be empty."),
            MaxLengthValidator(1000, message="Comment cannot exceed 1000 characters.")
        ],
        help_text="The content of the comment"
    )
    parent = models.ForeignKey(
        'self', 
        null=True, 
        blank=True, 
        on_delete=models.CASCADE, 
        related_name='replies',
        help_text="Parent comment if this is a reply"
    )
    is_edited = models.BooleanField(
        default=False,
        help_text="Whether this comment has been edited"
    )
    edited_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the comment was last edited"
    )
    is_deleted = models.BooleanField(
        default=False,
        help_text="Whether this comment has been deleted"
    )
    likes_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of likes on this comment"
    )

    class Meta:
        verbose_name = "Comment"
        verbose_name_plural = "Comments"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['post', '-created_at']),
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['parent', '-created_at']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.content[:50]}..."

    def save(self, *args, **kwargs):
        """Handle editing timestamp."""
        if self.pk:  # If this is an update
            self.is_edited = True
            self.edited_at = timezone.now()
        super().save(*args, **kwargs)

    def update_likes_count(self):
        """Update the likes count."""
        self.likes_count = self.likes.count()
        self.save(update_fields=['likes_count'])

    def is_liked_by(self, user):
        """Check if this comment is liked by a specific user."""
        return self.likes.filter(user=user).exists()

    def soft_delete(self):
        """Soft delete the comment."""
        self.is_deleted = True
        self.save(update_fields=['is_deleted'])


class Reaction(TimeStampedModel):
    """
    Reaction model for post and comment interactions.
    
    Supports different types of reactions (like, love, laugh, etc.).
    """
    
    REACTION_TYPES = [
        ('like', 'Like'),
        ('love', 'Love'),
        ('laugh', 'Laugh'),
        ('wow', 'Wow'),
        ('sad', 'Sad'),
        ('angry', 'Angry'),
    ]
    
    user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE,
        related_name='reactions',
        help_text="User who reacted"
    )
    reaction_type = models.CharField(
        max_length=10,
        choices=REACTION_TYPES,
        default='like',
        help_text="Type of reaction"
    )
    post = models.ForeignKey(
        Post,
        on_delete=models.CASCADE,
        related_name='reactions',
        null=True,
        blank=True,
        help_text="Post being reacted to"
    )
    comment = models.ForeignKey(
        Comment,
        on_delete=models.CASCADE,
        related_name='reactions',
        null=True,
        blank=True,
        help_text="Comment being reacted to"
    )

    class Meta:
        unique_together = [
            ('user', 'post'),
            ('user', 'comment'),
        ]
        verbose_name = "Reaction"
        verbose_name_plural = "Reactions"
        ordering = ['-created_at']

    def __str__(self):
        target = self.post or self.comment
        return f"{self.user.username} {self.reaction_type}s {target}"

    def save(self, *args, **kwargs):
        """Update counts when saving."""
        super().save(*args, **kwargs)
        if self.post:
            self.post.update_counts()
        elif self.comment:
            self.comment.update_likes_count()

    def delete(self, *args, **kwargs):
        """Update counts when deleting."""
        super().delete(*args, **kwargs)
        if self.post:
            self.post.update_counts()
        elif self.comment:
            self.comment.update_likes_count()


class Bookmark(TimeStampedModel):
    """
    Bookmark model for saving posts.
    """
    
    user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE,
        related_name='bookmarks',
        help_text="User who bookmarked"
    )
    post = models.ForeignKey(
        Post, 
        on_delete=models.CASCADE,
        related_name='bookmarks',
        help_text="Post being bookmarked"
    )

    class Meta:
        unique_together = ('user', 'post')
        verbose_name = "Bookmark"
        verbose_name_plural = "Bookmarks"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} bookmarked {self.post}"


class Share(TimeStampedModel):
    """
    Share model for reposting content.
    """
    
    user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE,
        related_name='shares',
        help_text="User who shared"
    )
    original_post = models.ForeignKey(
        Post, 
        on_delete=models.CASCADE,
        related_name='shares',
        help_text="Original post being shared"
    )
    share_text = models.TextField(
        blank=True,
        max_length=1000,
        help_text="Additional text when sharing"
    )

    class Meta:
        verbose_name = "Share"
        verbose_name_plural = "Shares"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} shared {self.original_post}"


class Notification(TimeStampedModel):
    """
    Notification model for user notifications.
    """
    
    NOTIFICATION_TYPES = [
        ('follow', 'Follow'),
        ('like', 'Like'),
        ('comment', 'Comment'),
        ('share', 'Share'),
        ('mention', 'Mention'),
    ]
    
    recipient = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='notifications',
        help_text="User receiving the notification"
    )
    sender = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sent_notifications',
        help_text="User who triggered the notification"
    )
    notification_type = models.CharField(
        max_length=20,
        choices=NOTIFICATION_TYPES,
        help_text="Type of notification"
    )
    post = models.ForeignKey(
        Post,
        on_delete=models.CASCADE,
        related_name='notifications',
        null=True,
        blank=True,
        help_text="Related post"
    )
    comment = models.ForeignKey(
        Comment,
        on_delete=models.CASCADE,
        related_name='notifications',
        null=True,
        blank=True,
        help_text="Related comment"
    )
    is_read = models.BooleanField(
        default=False,
        help_text="Whether the notification has been read"
    )

    class Meta:
        verbose_name = "Notification"
        verbose_name_plural = "Notifications"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['recipient', 'is_read']),
        ]

    def __str__(self):
        return f"{self.sender.username} {self.notification_type} - {self.recipient.username}"
