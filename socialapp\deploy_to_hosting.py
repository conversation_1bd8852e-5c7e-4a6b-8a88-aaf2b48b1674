#!/usr/bin/env python3
"""
Django SocialApp Deployment Helper
Prepares the app for deployment to various hosting platforms
"""

import os
import sys
import subprocess
import secrets
import string

def generate_secret_key():
    """Generate a secure Django secret key"""
    chars = string.ascii_letters + string.digits + '!@#$%^&*(-_=+)'
    return ''.join(secrets.choice(chars) for _ in range(50))

def create_env_file():
    """Create production .env file"""
    print("🔧 Creating production environment file...")
    
    # Get user input
    domain = input("Enter your domain (e.g., myapp.com): ").strip()
    if not domain:
        domain = "localhost"
    
    hosting_type = input("Enter hosting type (railway/render/pythonanywhere/other): ").strip().lower()
    
    # Generate secret key
    secret_key = generate_secret_key()
    
    # Create .env content
    env_content = f"""# Django SocialApp Production Environment
# Generated automatically - DO NOT COMMIT TO VERSION CONTROL

# Django Settings
DJANGO_ENVIRONMENT=production
DJANGO_DEBUG=False
DJANGO_SECRET_KEY={secret_key}
DJANGO_ALLOWED_HOSTS={domain},www.{domain},*.railway.app,*.render.com,*.pythonanywhere.com
DJANGO_CSRF_TRUSTED_ORIGINS=https://{domain},https://www.{domain}

# Database Configuration
# Will be auto-configured by hosting platform or set manually
DATABASE_URL=sqlite:///db.sqlite3

# Redis Configuration (for real-time chat)
# Will be auto-configured by hosting platform or set manually
REDIS_URL=redis://localhost:6379/0

# Email Configuration (optional)
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Security Settings
SECURE_SSL_REDIRECT=True
SECURE_PROXY_SSL_HEADER=HTTP_X_FORWARDED_PROTO,https
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True

# Static Files
STATIC_ROOT=staticfiles
MEDIA_ROOT=media
"""
    
    # Write .env file
    with open('.env', 'w') as f:
        f.write(env_content)
    
    print(f"✅ Created .env file for {hosting_type} hosting")
    print(f"🔑 Generated secure secret key")
    print(f"🌐 Configured for domain: {domain}")
    
    return hosting_type

def prepare_for_railway():
    """Prepare for Railway deployment"""
    print("\n🚂 Preparing for Railway deployment...")
    
    # Create railway.json if it doesn't exist
    railway_config = """{
  "$schema": "https://railway.app/railway.schema.json",
  "build": {
    "builder": "NIXPACKS"
  },
  "deploy": {
    "startCommand": "python manage.py migrate && python manage.py collectstatic --noinput && gunicorn --config gunicorn.conf.py socialapp.wsgi:application",
    "healthcheckPath": "/",
    "healthcheckTimeout": 100,
    "restartPolicyType": "ON_FAILURE",
    "restartPolicyMaxRetries": 10
  }
}"""
    
    with open('railway.json', 'w') as f:
        f.write(railway_config)
    
    print("✅ Created railway.json configuration")
    print("\n📋 Railway Deployment Steps:")
    print("1. Install Railway CLI: npm install -g @railway/cli")
    print("2. Login: railway login")
    print("3. Initialize: railway init")
    print("4. Add PostgreSQL: railway add postgresql")
    print("5. Add Redis: railway add redis")
    print("6. Deploy: railway up")

def prepare_for_render():
    """Prepare for Render deployment"""
    print("\n🎨 Preparing for Render deployment...")
    
    print("✅ render.yaml already exists")
    print("\n📋 Render Deployment Steps:")
    print("1. Push code to GitHub")
    print("2. Connect GitHub repo to Render")
    print("3. Render will auto-detect and deploy")
    print("4. Add PostgreSQL and Redis services in dashboard")

def prepare_for_pythonanywhere():
    """Prepare for PythonAnywhere deployment"""
    print("\n🐍 Preparing for PythonAnywhere deployment...")
    
    # Create a simple WSGI file
    wsgi_content = """import os
import sys

# Add your project directory to sys.path
path = '/home/<USER>/socialapp'
if path not in sys.path:
    sys.path.insert(0, path)

os.environ['DJANGO_SETTINGS_MODULE'] = 'socialapp.settings'

from django.core.wsgi import get_wsgi_application
application = get_wsgi_application()
"""
    
    with open('pythonanywhere_wsgi.py', 'w') as f:
        f.write(wsgi_content)
    
    print("✅ Created PythonAnywhere WSGI file")
    print("\n📋 PythonAnywhere Deployment Steps:")
    print("1. Upload files to PythonAnywhere")
    print("2. Create virtual environment")
    print("3. Install requirements: pip install -r requirements-production.txt")
    print("4. Configure web app in dashboard")
    print("5. Set WSGI file path")
    print("⚠️  Note: WebSocket support limited on free tier")

def run_deployment_checks():
    """Run Django deployment checks"""
    print("\n🔍 Running deployment checks...")
    
    try:
        # Install requirements
        print("📦 Installing requirements...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements-production.txt'], 
                      check=True, capture_output=True)
        
        # Collect static files
        print("📁 Collecting static files...")
        subprocess.run([sys.executable, 'manage.py', 'collectstatic', '--noinput'], 
                      check=True, capture_output=True)
        
        # Run Django checks
        print("✅ Running Django deployment checks...")
        result = subprocess.run([sys.executable, 'manage.py', 'check', '--deploy'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ All deployment checks passed!")
        else:
            print("⚠️  Some deployment warnings:")
            print(result.stdout)
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Error during checks: {e}")
        print("Please install requirements manually and try again")

def main():
    """Main deployment preparation function"""
    print("🚀 Django SocialApp Deployment Helper")
    print("=====================================")
    
    # Change to project directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # Create environment file
    hosting_type = create_env_file()
    
    # Prepare for specific hosting
    if hosting_type == 'railway':
        prepare_for_railway()
    elif hosting_type == 'render':
        prepare_for_render()
    elif hosting_type == 'pythonanywhere':
        prepare_for_pythonanywhere()
    else:
        print(f"\n🔧 Generic deployment preparation for {hosting_type}")
        print("📋 General Steps:")
        print("1. Upload your code to the hosting platform")
        print("2. Install requirements: pip install -r requirements-production.txt")
        print("3. Set environment variables from .env file")
        print("4. Run migrations: python manage.py migrate")
        print("5. Collect static files: python manage.py collectstatic")
        print("6. Start with: gunicorn socialapp.wsgi:application")
    
    # Run deployment checks
    run_deployment_checks()
    
    print("\n🎉 Deployment preparation complete!")
    print("\n⚠️  Important reminders:")
    print("- Never commit the .env file to version control")
    print("- Set up PostgreSQL and Redis for production")
    print("- Configure your domain's DNS settings")
    print("- Set up SSL certificate (Let's Encrypt recommended)")
    print("- Test real-time chat functionality after deployment")

if __name__ == '__main__':
    main()
