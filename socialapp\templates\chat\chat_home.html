{% extends 'base.html' %}
{% load static %}

{% block title %}Chat - SocialApp{% endblock %}

{% block extra_css %}
<style>
    .chat-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .chat-sidebar {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .chat-room-item {
        display: flex;
        align-items: center;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 10px;
        text-decoration: none;
        color: inherit;
        transition: background-color 0.2s;
    }
    
    .chat-room-item:hover {
        background-color: #f8f9fa;
        text-decoration: none;
        color: inherit;
    }
    
    .chat-room-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        margin-right: 15px;
        background: #007bff;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
    }
    
    .chat-room-info {
        flex: 1;
    }
    
    .chat-room-name {
        font-weight: 600;
        margin-bottom: 5px;
    }
    
    .chat-room-last-message {
        color: #6c757d;
        font-size: 0.9em;
    }
    
    .chat-room-meta {
        text-align: right;
    }
    
    .chat-room-time {
        color: #6c757d;
        font-size: 0.8em;
    }
    
    .unread-badge {
        background: #dc3545;
        color: white;
        border-radius: 50%;
        padding: 2px 6px;
        font-size: 0.7em;
        margin-top: 5px;
    }
    
    .online-users {
        margin-top: 30px;
    }
    
    .online-user {
        display: flex;
        align-items: center;
        padding: 10px;
        border-radius: 8px;
        margin-bottom: 5px;
        text-decoration: none;
        color: inherit;
    }
    
    .online-user:hover {
        background-color: #f8f9fa;
        text-decoration: none;
        color: inherit;
    }
    
    .online-indicator {
        width: 10px;
        height: 10px;
        background: #28a745;
        border-radius: 50%;
        margin-left: auto;
    }
    
    .action-buttons {
        margin-bottom: 20px;
    }
    
    .btn-chat {
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 8px;
        text-decoration: none;
        margin-right: 10px;
    }
    
    .btn-chat:hover {
        background: #0056b3;
        color: white;
        text-decoration: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="chat-container">
    <div class="row">
        <div class="col-md-8">
            <div class="chat-sidebar">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4>Your Chats</h4>
                    <div class="action-buttons">
                        <a href="{% url 'create_group_chat' %}" class="btn-chat">
                            <i class="fas fa-plus"></i> New Group
                        </a>
                        <a href="{% url 'chat_search' %}" class="btn-chat">
                            <i class="fas fa-search"></i> Find Users
                        </a>
                    </div>
                </div>
                
                {% if chat_rooms %}
                    {% for room in chat_rooms %}
                        <a href="{% url 'chat_room' room_id=room.id %}" class="chat-room-item">
                            <div class="chat-room-avatar">
                                {% if room.room_type == 'private' %}
                                    {% for participant in room.participants.all %}
                                        {% if participant != request.user %}
                                            {{ participant.username|first|upper }}
                                        {% endif %}
                                    {% endfor %}
                                {% else %}
                                    {{ room.name|first|upper }}
                                {% endif %}
                            </div>
                            <div class="chat-room-info">
                                <div class="chat-room-name">
                                    {% if room.room_type == 'private' %}
                                        {% for participant in room.participants.all %}
                                            {% if participant != request.user %}
                                                {{ participant.get_full_name|default:participant.username }}
                                            {% endif %}
                                        {% endfor %}
                                    {% else %}
                                        {{ room.name }}
                                    {% endif %}
                                </div>
                                {% if room.last_message %}
                                    <div class="chat-room-last-message">
                                        {{ room.last_message.sender.username }}: {{ room.last_message.content|truncatechars:50 }}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="chat-room-meta">
                                {% if room.last_message %}
                                    <div class="chat-room-time">
                                        {{ room.last_message.created_at|timesince }} ago
                                    </div>
                                {% endif %}
                                {% if room.unread_count > 0 %}
                                    <div class="unread-badge">{{ room.unread_count }}</div>
                                {% endif %}
                            </div>
                        </a>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No chats yet</h5>
                        <p class="text-muted">Start a conversation with someone!</p>
                        <a href="{% url 'chat_search' %}" class="btn btn-primary">Find Users to Chat</a>
                    </div>
                {% endif %}
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="chat-sidebar">
                <h5 class="mb-3">Online Users</h5>
                {% if active_users %}
                    {% for user in active_users %}
                        <a href="{% url 'start_chat' username=user.username %}" class="online-user">
                            <div class="chat-room-avatar" style="width: 35px; height: 35px; margin-right: 10px;">
                                {{ user.username|first|upper }}
                            </div>
                            <div>
                                <div style="font-weight: 500;">{{ user.get_full_name|default:user.username }}</div>
                                <small class="text-muted">@{{ user.username }}</small>
                            </div>
                            <div class="online-indicator"></div>
                        </a>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">No users online</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh chat list every 30 seconds
    setInterval(function() {
        location.reload();
    }, 30000);
</script>
{% endblock %}
