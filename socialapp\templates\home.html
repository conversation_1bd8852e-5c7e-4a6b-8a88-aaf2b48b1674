{% extends 'base.html' %}

{% block title %}Home - BlogApp{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-home"></i> Home</h2>
    {% if user.is_authenticated %}
    <a href="{% url 'create_post' %}" class="btn btn-primary">
        <i class="fas fa-plus"></i> New Post
    </a>
    {% endif %}
</div>

{% if user.is_authenticated %}
<!-- Welcome Message -->
<div class="card mb-4">
    <div class="card-body">
        <h5 class="card-title">Welcome back, {{ user.username }}!</h5>
        <p class="card-text">Share what's on your mind with the world.</p>
        <a href="{% url 'create_post' %}" class="btn btn-primary">
            <i class="fas fa-edit"></i> Write a post
        </a>
    </div>
</div>
{% else %}
<!-- Welcome for Anonymous Users -->
<div class="card mb-4">
    <div class="card-body text-center">
        <h5 class="card-title">Welcome to BlogApp</h5>
        <p class="card-text">Join our community and start sharing your thoughts with the world.</p>
        <div class="d-flex justify-content-center gap-2">
            <a href="{% url 'register' %}" class="btn btn-primary">
                <i class="fas fa-user-plus"></i> Sign Up
            </a>
            <a href="{% url 'login' %}" class="btn btn-outline-primary">
                <i class="fas fa-sign-in-alt"></i> Login
            </a>
        </div>
    </div>
</div>
{% endif %}

<!-- Posts Feed -->
{% if posts %}
    {% for post in posts %}
    <div class="card post-card">
        <!-- Post Header -->
        <div class="post-header">
            <div class="d-flex align-items-center">
                <img src="{{ post.user.profile.get_avatar_url }}" 
                     alt="{{ post.user.username }}" class="avatar me-3">
                <div class="flex-grow-1">
                    <div class="d-flex align-items-center">
                        <h6 class="mb-0 fw-bold">{{ post.user.username }}</h6>
                        {% if post.user.profile.is_verified %}
                        <i class="fas fa-check-circle text-primary ms-1" title="Verified"></i>
                        {% endif %}
                        {% if post.is_edited %}
                        <small class="text-muted ms-2">(edited)</small>
                        {% endif %}
                    </div>
                    <small class="text-muted">
                        <i class="fas fa-clock"></i> {{ post.created_at|timesince }} ago
                        {% if not post.is_public %}
                        <i class="fas fa-lock ms-2" title="Private post"></i>
                        {% endif %}
                    </small>
                </div>
                {% if user == post.user %}
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                            data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{% url 'edit_post' post.pk %}">
                            <i class="fas fa-edit"></i> Edit
                        </a></li>
                        <li><a class="dropdown-item text-danger" href="{% url 'delete_post' post.pk %}">
                            <i class="fas fa-trash"></i> Delete
                        </a></li>
                    </ul>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Post Content -->
        <div class="post-content">
            <p class="mb-3">{{ post.content|linebreaks }}</p>
            
            <!-- Hashtags -->
            {% if post.hashtags.all %}
            <div class="mb-3">
                {% for hashtag in post.hashtags.all %}
                <a href="{% url 'hashtag_detail' hashtag.slug %}" class="hashtag me-2">
                    #{{ hashtag.name }}
                </a>
                {% endfor %}
            </div>
            {% endif %}
            
            <!-- Post Media -->
            {% if post.image %}
            <div class="mb-3">
                <img src="{{ post.image.url }}" alt="Post image" class="img-fluid rounded">
            </div>
            {% endif %}
            
            {% if post.video %}
            <div class="mb-3">
                <video controls class="w-100 rounded">
                    <source src="{{ post.video.url }}" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
            </div>
            {% endif %}
        </div>

        <!-- Post Stats -->
        <div class="px-3 pb-2" data-post-id="{{ post.pk }}">
            <div class="d-flex justify-content-between text-muted small">
                <span class="likes-count"><i class="fas fa-heart"></i> {{ post.likes_count }} likes</span>
                <span><i class="fas fa-comment"></i> {{ post.comments_count }} comments</span>
                <span><i class="fas fa-share"></i> {{ post.shares_count }} shares</span>
                <span><i class="fas fa-eye"></i> {{ post.views_count }} views</span>
            </div>
        </div>

        <!-- Post Actions -->
        <div class="post-actions">
            <div class="d-flex justify-content-between">
                <!-- Like Button -->
                {% if user.is_authenticated %}
                <form method="post" action="{% url 'toggle_reaction' post.pk %}" class="d-inline">
                    {% csrf_token %}
                    <input type="hidden" name="reaction_type" value="like">
                    <button type="submit" class="action-btn reaction-btn {% if post.is_liked_by %}liked{% endif %}" 
                            title="Like">
                        <i class="fas fa-heart"></i>
                    </button>
                </form>
                {% else %}
                <button class="action-btn" disabled title="Login to like">
                    <i class="fas fa-heart"></i>
                </button>
                {% endif %}

                <!-- Comment Button -->
                <a href="{% url 'post_detail' post.pk %}" class="action-btn" title="Comment">
                    <i class="fas fa-comment"></i>
                </a>

                <!-- Share Button -->
                {% if user.is_authenticated %}
                <form method="post" action="{% url 'share_post' post.pk %}" class="d-inline">
                    {% csrf_token %}
                    <button type="submit" class="action-btn" title="Share">
                        <i class="fas fa-share"></i>
                    </button>
                </form>
                {% else %}
                <button class="action-btn" disabled title="Login to share">
                    <i class="fas fa-share"></i>
                </button>
                {% endif %}

                <!-- Bookmark Button -->
                {% if user.is_authenticated %}
                <form method="post" action="{% url 'toggle_bookmark' post.pk %}" class="d-inline">
                    {% csrf_token %}
                    <button type="submit" class="action-btn {% if post.is_bookmarked_by %}bookmarked{% endif %}" 
                            title="Bookmark">
                        <i class="fas fa-bookmark"></i>
                    </button>
                </form>
                {% else %}
                <button class="action-btn" disabled title="Login to bookmark">
                    <i class="fas fa-bookmark"></i>
                </button>
                {% endif %}
            </div>
        </div>
    </div>
    {% endfor %}

    <!-- Pagination -->
    {% if posts.has_other_pages %}
    <nav aria-label="Posts pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if posts.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page={{ posts.previous_page_number }}">
                    <i class="fas fa-chevron-left"></i> Previous
                </a>
            </li>
            {% endif %}

            {% for num in posts.paginator.page_range %}
                {% if posts.number == num %}
                <li class="page-item active">
                    <span class="page-link">{{ num }}</span>
                </li>
                {% elif num > posts.number|add:'-3' and num < posts.number|add:'3' %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                </li>
                {% endif %}
            {% endfor %}

            {% if posts.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ posts.next_page_number }}">
                    Next <i class="fas fa-chevron-right"></i>
                </a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}

{% else %}
<!-- No Posts -->
<div class="card">
    <div class="card-body text-center py-5">
        <i class="fas fa-feather fa-3x text-muted mb-3"></i>
        <h5>No posts yet</h5>
        <p class="text-muted">
            {% if user.is_authenticated %}
            Be the first to share something!
            {% else %}
            Sign up to see posts from the community.
            {% endif %}
        </p>
        {% if user.is_authenticated %}
        <a href="{% url 'create_post' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Create your first post
        </a>
        {% else %}
        <a href="{% url 'register' %}" class="btn btn-primary">
            <i class="fas fa-user-plus"></i> Join BlogApp
        </a>
        {% endif %}
    </div>
</div>
{% endif %}
{% endblock %}
