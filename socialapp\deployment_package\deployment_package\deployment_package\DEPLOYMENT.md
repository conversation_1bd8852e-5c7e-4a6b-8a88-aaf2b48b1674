# 🚀 Django SocialApp Deployment Guide

This guide covers deploying your Django social media app with real-time chat functionality to various hosting platforms.

## 📋 Pre-Deployment Checklist

- [ ] Domain name configured
- [ ] SSL certificate ready (Let's Encrypt recommended)
- [ ] Database setup (PostgreSQL recommended)
- [ ] Redis instance for real-time features
- [ ] Environment variables configured
- [ ] Static files collected
- [ ] Database migrations applied

## 🌐 Deployment Options

### Option 1: Railway (Recommended for Beginners)

**Pros:** Free tier, automatic deployments, built-in database and Redis
**Cons:** Limited free tier resources

1. **Setup:**
   ```bash
   # Install Railway CLI
   npm install -g @railway/cli
   
   # Login to Railway
   railway login
   
   # Initialize project
   railway init
   ```

2. **Deploy:**
   ```bash
   # Deploy to Railway
   railway up
   
   # Add environment variables
   railway variables set DJANGO_SECRET_KEY=your-secret-key
   railway variables set DJANGO_ALLOWED_HOSTS=your-domain.railway.app
   ```

3. **Add Database and Redis:**
   - Go to Railway dashboard
   - Add PostgreSQL plugin
   - Add Redis plugin
   - Environment variables will be auto-configured

### Option 2: Render (Great Free Tier)

**Pros:** Generous free tier, automatic SSL, easy setup
**Cons:** Cold starts on free tier

1. **Setup:**
   - Connect your GitHub repository to Render
   - Use the provided `render.yaml` configuration
   - Render will automatically detect and deploy

2. **Environment Variables:**
   - Set in Render dashboard
   - Database and Redis URLs auto-configured

### Option 3: PythonAnywhere (Python-Focused)

**Pros:** Python-specialized, good free tier
**Cons:** Limited WebSocket support on free tier

1. **Upload Files:**
   ```bash
   # Zip your project
   zip -r socialapp.zip . -x "*.git*" "*__pycache__*" "*.pyc"
   ```

2. **Setup on PythonAnywhere:**
   - Upload zip file
   - Extract in your home directory
   - Configure web app in dashboard

### Option 4: Your Own Server (VPS/Dedicated)

**Pros:** Full control, no limitations
**Cons:** Requires server management skills

## 🔧 Server Setup (Ubuntu/Debian)

### 1. Install Dependencies

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Python and dependencies
sudo apt install python3 python3-pip python3-venv nginx postgresql postgresql-contrib redis-server

# Install Node.js (for WebSocket support)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs
```

### 2. Setup Database

```bash
# Create PostgreSQL database
sudo -u postgres psql
CREATE DATABASE socialapp_db;
CREATE USER socialapp_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE socialapp_db TO socialapp_user;
\q
```

### 3. Deploy Application

```bash
# Clone your repository
git clone https://github.com/yourusername/socialapp.git
cd socialapp

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements-production.txt

# Setup environment
cp .env.example .env
# Edit .env with your settings

# Run deployment script
chmod +x deploy.sh
./deploy.sh
```

### 4. Configure Nginx

```bash
# Copy nginx configuration
sudo cp nginx.conf /etc/nginx/sites-available/socialapp
sudo ln -s /etc/nginx/sites-available/socialapp /etc/nginx/sites-enabled/
sudo rm /etc/nginx/sites-enabled/default

# Test configuration
sudo nginx -t

# Restart nginx
sudo systemctl restart nginx
```

### 5. Setup SSL with Let's Encrypt

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 6. Create Systemd Service

```bash
# Create service file
sudo nano /etc/systemd/system/socialapp.service
```

Add this content:
```ini
[Unit]
Description=Django SocialApp
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/path/to/socialapp
Environment="PATH=/path/to/socialapp/venv/bin"
ExecStart=/path/to/socialapp/venv/bin/gunicorn --config gunicorn.conf.py socialapp.wsgi:application
Restart=always

[Install]
WantedBy=multi-user.target
```

```bash
# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable socialapp
sudo systemctl start socialapp
```

## 🔒 Security Considerations

1. **Environment Variables:**
   - Never commit `.env` files
   - Use strong secret keys
   - Rotate keys regularly

2. **Database Security:**
   - Use strong passwords
   - Limit database access
   - Regular backups

3. **Server Security:**
   - Keep system updated
   - Use firewall (ufw)
   - Monitor logs

## 📊 Monitoring

1. **Application Monitoring:**
   - Setup Sentry for error tracking
   - Monitor performance metrics
   - Set up health checks

2. **Server Monitoring:**
   - Monitor CPU/Memory usage
   - Check disk space
   - Monitor network traffic

## 🔄 Maintenance

1. **Regular Updates:**
   ```bash
   # Update dependencies
   pip install -r requirements-production.txt --upgrade
   
   # Run migrations
   python manage.py migrate
   
   # Collect static files
   python manage.py collectstatic --noinput
   
   # Restart service
   sudo systemctl restart socialapp
   ```

2. **Backup Strategy:**
   - Database backups
   - Media files backup
   - Configuration backup

## 🆘 Troubleshooting

### Common Issues:

1. **Static files not loading:**
   ```bash
   python manage.py collectstatic --noinput
   sudo systemctl restart nginx
   ```

2. **Database connection errors:**
   - Check database credentials
   - Verify database is running
   - Check firewall settings

3. **WebSocket not working:**
   - Verify Redis is running
   - Check nginx WebSocket configuration
   - Ensure proper ASGI setup

### Logs:

```bash
# Application logs
sudo journalctl -u socialapp -f

# Nginx logs
sudo tail -f /var/log/nginx/error.log

# Database logs
sudo tail -f /var/log/postgresql/postgresql-*.log
```

## 📞 Support

If you encounter issues:
1. Check the logs first
2. Verify environment variables
3. Test database connectivity
4. Check firewall settings
5. Review nginx configuration

Remember to test your deployment thoroughly before going live!
