# type: ignore
"""
Forms for the social blogging platform.

This module contains form classes for user input validation and processing.
"""

from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.utils.html import strip_tags
from .models import Profile, Post, Comment
import re


class CustomUserCreationForm(UserCreationForm):
    """Enhanced user registration form with email validation."""
    
    email = forms.EmailField(
        required=True,
        help_text="Required. Enter a valid email address."
    )
    first_name = forms.CharField(
        max_length=30,
        required=False,
        help_text="Optional."
    )
    last_name = forms.CharField(
        max_length=30,
        required=False,
        help_text="Optional."
    )

    class Meta:
        model = User
        fields = ("username", "email", "first_name", "last_name", "password1", "password2")

    def clean_email(self):
        """Validate email uniqueness."""
        email = self.cleaned_data.get('email')
        if User.objects.filter(email=email).exists():
            raise ValidationError("A user with this email already exists.")
        return email

    def clean_username(self):
        """Validate username format."""
        username = self.cleaned_data.get('username')
        if not re.match(r'^[a-zA-Z0-9_]+$', username):
            raise ValidationError("Username can only contain letters, numbers, and underscores.")
        return username

    def save(self, commit=True):
        """Save user with email."""
        user = super().save(commit=False)
        user.email = self.cleaned_data["email"]
        user.first_name = self.cleaned_data.get("first_name", "")
        user.last_name = self.cleaned_data.get("last_name", "")
        if commit:
            user.save()
        return user


class ProfileForm(forms.ModelForm):
    """Form for editing user profiles."""
    
    class Meta:
        model = Profile
        fields = ['bio', 'avatar', 'cover_photo', 'date_of_birth', 'location', 'website']
        widgets = {
            'bio': forms.Textarea(attrs={
                'rows': 4,
                'placeholder': 'Tell us about yourself...',
                'class': 'form-control'
            }),
            'date_of_birth': forms.DateInput(attrs={
                'type': 'date',
                'class': 'form-control'
            }),
            'location': forms.TextInput(attrs={
                'placeholder': 'Your location',
                'class': 'form-control'
            }),
            'website': forms.URLInput(attrs={
                'placeholder': 'https://yourwebsite.com',
                'class': 'form-control'
            }),
            'avatar': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*'
            }),
            'cover_photo': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*'
            }),
        }

    def clean_bio(self):
        """Clean and validate bio content."""
        bio = self.cleaned_data.get('bio')
        if bio:
            # Strip HTML tags for security
            bio = strip_tags(bio)
            # Check length
            if len(bio) > 500:
                raise ValidationError("Bio cannot exceed 500 characters.")
        return bio

    def clean_website(self):
        """Validate website URL."""
        website = self.cleaned_data.get('website')
        if website and not website.startswith(('http://', 'https://')):
            website = 'https://' + website
        return website


class PostForm(forms.ModelForm):
    """Form for creating and editing posts."""
    
    class Meta:
        model = Post
        fields = ['content', 'image', 'video', 'is_public']
        widgets = {
            'content': forms.Textarea(attrs={
                'rows': 6,
                'placeholder': 'What\'s on your mind?',
                'class': 'form-control',
                'maxlength': '10000'
            }),
            'image': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*'
            }),
            'video': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'video/*'
            }),
            'is_public': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }

    def clean_content(self):
        """Clean and validate post content."""
        content = self.cleaned_data.get('content')
        if not content or not content.strip():
            raise ValidationError("Post content cannot be empty.")
        
        # Strip HTML tags for security
        content = strip_tags(content).strip()
        
        # Check length
        if len(content) > 10000:
            raise ValidationError("Post cannot exceed 10,000 characters.")
        
        return content

    def clean(self):
        """Validate that at least content or media is provided."""
        cleaned_data = super().clean()
        content = cleaned_data.get('content')
        image = cleaned_data.get('image')
        video = cleaned_data.get('video')
        
        if not content and not image and not video:
            raise ValidationError("Post must have either content or media.")
        
        return cleaned_data


class CommentForm(forms.ModelForm):
    """Form for creating comments."""
    
    class Meta:
        model = Comment
        fields = ['content']
        widgets = {
            'content': forms.Textarea(attrs={
                'rows': 3,
                'placeholder': 'Write a comment...',
                'class': 'form-control',
                'maxlength': '1000'
            }),
        }

    def clean_content(self):
        """Clean and validate comment content."""
        content = self.cleaned_data.get('content')
        if not content or not content.strip():
            raise ValidationError("Comment cannot be empty.")
        
        # Strip HTML tags for security
        content = strip_tags(content).strip()
        
        # Check length
        if len(content) > 1000:
            raise ValidationError("Comment cannot exceed 1,000 characters.")
        
        return content


class SearchForm(forms.Form):
    """Form for search functionality."""
    
    SEARCH_TYPES = [
        ('all', 'All'),
        ('posts', 'Posts'),
        ('users', 'Users'),
        ('hashtags', 'Hashtags'),
    ]
    
    q = forms.CharField(
        max_length=100,
        required=True,
        widget=forms.TextInput(attrs={
            'placeholder': 'Search...',
            'class': 'form-control search-box'
        })
    )
    type = forms.ChoiceField(
        choices=SEARCH_TYPES,
        required=False,
        initial='all',
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )

    def clean_q(self):
        """Clean and validate search query."""
        query = self.cleaned_data.get('q')
        if query:
            # Strip HTML tags for security
            query = strip_tags(query).strip()
            
            # Minimum length check
            if len(query) < 2:
                raise ValidationError("Search query must be at least 2 characters long.")
            
            # Maximum length check
            if len(query) > 100:
                raise ValidationError("Search query cannot exceed 100 characters.")
        
        return query
