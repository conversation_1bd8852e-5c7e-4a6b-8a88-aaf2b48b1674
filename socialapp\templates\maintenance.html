<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Maintenance - BlogApp</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .maintenance-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        .maintenance-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 1.5rem;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        .maintenance-title {
            color: #333;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        .maintenance-text {
            color: #666;
            line-height: 1.6;
            margin-bottom: 2rem;
        }
        .progress-bar {
            height: 8px;
            border-radius: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            animation: loading 3s infinite;
        }
        @keyframes loading {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }
        .social-links {
            margin-top: 2rem;
        }
        .social-links a {
            color: #667eea;
            font-size: 1.5rem;
            margin: 0 0.5rem;
            transition: color 0.3s ease;
        }
        .social-links a:hover {
            color: #764ba2;
        }
    </style>
</head>
<body>
    <div class="maintenance-card">
        <div class="maintenance-icon">
            <i class="fas fa-tools"></i>
        </div>
        
        <h1 class="maintenance-title">We'll be back soon!</h1>
        
        <p class="maintenance-text">
            We're currently performing scheduled maintenance to improve your experience. 
            Our team is working hard to get everything back online as quickly as possible.
        </p>
        
        <div class="progress mb-3">
            <div class="progress-bar" role="progressbar"></div>
        </div>
        
        <p class="text-muted small">
            <i class="fas fa-clock"></i> 
            Expected completion: Within the next hour
        </p>
        
        <div class="social-links">
            <p class="text-muted mb-2">Stay updated:</p>
            <a href="#" title="Twitter"><i class="fab fa-twitter"></i></a>
            <a href="#" title="Facebook"><i class="fab fa-facebook"></i></a>
            <a href="#" title="Instagram"><i class="fab fa-instagram"></i></a>
            <a href="#" title="LinkedIn"><i class="fab fa-linkedin"></i></a>
        </div>
        
        <div class="mt-4">
            <small class="text-muted">
                If you need immediate assistance, please contact us at 
                <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
            </small>
        </div>
    </div>

    <script>
        // Auto-refresh every 5 minutes
        setTimeout(function() {
            window.location.reload();
        }, 300000);
    </script>
</body>
</html>
