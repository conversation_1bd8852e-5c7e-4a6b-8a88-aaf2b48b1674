#!/bin/bash
# Django SocialApp Deployment Script

echo "🚀 Starting Django SocialApp Deployment..."

# Exit on any error
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    print_warning "Creating virtual environment..."
    python -m venv venv
fi

# Activate virtual environment
print_status "Activating virtual environment..."
source venv/bin/activate

# Install production requirements
print_status "Installing production requirements..."
pip install -r requirements-production.txt

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_warning "Creating .env file from template..."
    cp .env.example .env
    print_error "Please edit .env file with your production settings!"
    exit 1
fi

# Collect static files
print_status "Collecting static files..."
python manage.py collectstatic --noinput

# Run database migrations
print_status "Running database migrations..."
python manage.py migrate

# Create superuser if it doesn't exist
print_status "Checking for superuser..."
python manage.py shell -c "
from django.contrib.auth.models import User
if not User.objects.filter(is_superuser=True).exists():
    print('Creating superuser...')
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('Superuser created: admin/admin123')
else:
    print('Superuser already exists')
"

# Check deployment readiness
print_status "Checking deployment readiness..."
python manage.py check --deploy

print_status "Deployment preparation complete!"
print_warning "Remember to:"
print_warning "1. Update .env file with your production settings"
print_warning "2. Set up your web server (Nginx/Apache)"
print_warning "3. Configure your database"
print_warning "4. Set up Redis for real-time features"
print_warning "5. Configure SSL certificate"

echo "🎉 Ready for deployment!"
