{% extends 'base.html' %}

{% block title %}Edit Profile - BlogApp{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0"><i class="fas fa-user-edit"></i> Edit Profile</h4>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    <!-- Profile Picture Section -->
                    <div class="text-center mb-4">
                        <div class="position-relative d-inline-block">
                            <img src="{{ user.profile.get_avatar_url }}" 
                                 alt="{{ user.username }}" 
                                 class="rounded-circle mb-3" 
                                 style="width: 120px; height: 120px; object-fit: cover; border: 3px solid #e1e8ed;">
                            <div class="position-absolute bottom-0 end-0">
                                <label for="avatar" class="btn btn-primary btn-sm rounded-circle" 
                                       style="width: 35px; height: 35px; padding: 0; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-camera"></i>
                                </label>
                                <input type="file" id="avatar" name="avatar" accept="image/*" style="display: none;">
                            </div>
                        </div>
                        <p class="text-muted small">Click the camera icon to change your profile picture</p>
                    </div>

                    <!-- Cover Photo Section -->
                    <div class="mb-4">
                        <label class="form-label">Cover Photo</label>
                        <div class="position-relative">
                            <img src="{{ user.profile.get_cover_url }}" 
                                 alt="Cover photo" 
                                 class="w-100 rounded" 
                                 style="height: 200px; object-fit: cover; border: 1px solid #e1e8ed;">
                            <div class="position-absolute top-0 end-0 m-2">
                                <label for="cover_photo" class="btn btn-primary btn-sm">
                                    <i class="fas fa-camera"></i> Change Cover
                                </label>
                                <input type="file" id="cover_photo" name="cover_photo" accept="image/*" style="display: none;">
                            </div>
                        </div>
                        <div class="form-text">Recommended size: 1200x400 pixels. Max 10MB.</div>
                    </div>

                    <!-- Basic Information -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">Username</label>
                                <input type="text" class="form-control" id="username" value="{{ user.username }}" readonly>
                                <div class="form-text">Username cannot be changed</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" value="{{ user.email }}" readonly>
                                <div class="form-text">Email cannot be changed here</div>
                            </div>
                        </div>
                    </div>

                    <!-- Bio Section -->
                    <div class="mb-3">
                        <label for="bio" class="form-label">Bio</label>
                        <textarea class="form-control" id="bio" name="bio" rows="4" 
                                  placeholder="Tell us about yourself..." maxlength="500">{{ user.profile.bio }}</textarea>
                        <div class="form-text">
                            <span id="bio-count">{{ user.profile.bio|length }}</span>/500 characters
                        </div>
                    </div>

                    <!-- Personal Information -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="location" class="form-label">
                                    <i class="fas fa-map-marker-alt"></i> Location
                                </label>
                                <input type="text" class="form-control" id="location" name="location" 
                                       value="{{ user.profile.location }}" placeholder="Where are you from?">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="website" class="form-label">
                                    <i class="fas fa-globe"></i> Website
                                </label>
                                <input type="url" class="form-control" id="website" name="website" 
                                       value="{{ user.profile.website }}" placeholder="https://yourwebsite.com">
                            </div>
                        </div>
                    </div>

                    <!-- Date of Birth -->
                    <div class="mb-3">
                        <label for="date_of_birth" class="form-label">
                            <i class="fas fa-birthday-cake"></i> Date of Birth
                        </label>
                        <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" 
                               value="{{ user.profile.date_of_birth|date:'Y-m-d' }}">
                    </div>

                    <!-- Privacy Settings -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-shield-alt"></i> Privacy Settings</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="show_email" name="show_email">
                                <label class="form-check-label" for="show_email">
                                    Show email address on profile
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="show_birthday" name="show_birthday">
                                <label class="form-check-label" for="show_birthday">
                                    Show birthday on profile
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'profile' user.username %}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Profile Statistics -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-bar"></i> Profile Statistics</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-4">
                        <div class="border-end">
                            <h4 class="text-primary">{{ user.profile.posts_count }}</h4>
                            <p class="text-muted mb-0">Posts</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="border-end">
                            <h4 class="text-success">{{ user.profile.followers_count }}</h4>
                            <p class="text-muted mb-0">Followers</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <h4 class="text-info">{{ user.profile.following_count }}</h4>
                        <p class="text-muted mb-0">Following</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Account Actions -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-cog"></i> Account Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="#" class="btn btn-outline-warning">
                        <i class="fas fa-key"></i> Change Password
                    </a>
                    <a href="#" class="btn btn-outline-info">
                        <i class="fas fa-bell"></i> Notification Settings
                    </a>
                    <a href="#" class="btn btn-outline-danger">
                        <i class="fas fa-trash"></i> Delete Account
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const bioTextarea = document.getElementById('bio');
    const bioCount = document.getElementById('bio-count');
    const avatarInput = document.getElementById('avatar');
    const coverInput = document.getElementById('cover_photo');
    const avatarPreview = document.querySelector('img[alt="{{ user.username }}"]');
    const coverPreview = document.querySelector('img[alt="Cover photo"]');

    // Bio character counter
    bioTextarea.addEventListener('input', function() {
        const count = this.value.length;
        bioCount.textContent = count;
        
        if (count > 450) {
            bioCount.classList.add('text-danger');
        } else {
            bioCount.classList.remove('text-danger');
        }
    });

    // Avatar preview
    avatarInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            if (file.size > 5 * 1024 * 1024) {
                alert('Avatar file size must be less than 5MB');
                this.value = '';
                return;
            }
            
            const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
            if (!allowedTypes.includes(file.type)) {
                alert('Avatar must be a JPEG, PNG, or GIF image');
                this.value = '';
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                avatarPreview.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    });

    // Cover photo preview
    coverInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            if (file.size > 10 * 1024 * 1024) {
                alert('Cover photo file size must be less than 10MB');
                this.value = '';
                return;
            }
            
            const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
            if (!allowedTypes.includes(file.type)) {
                alert('Cover photo must be a JPEG, PNG, or GIF image');
                this.value = '';
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                coverPreview.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    });

    // Website URL validation
    const websiteInput = document.getElementById('website');
    websiteInput.addEventListener('blur', function() {
        const url = this.value.trim();
        if (url && !url.startsWith('http://') && !url.startsWith('https://')) {
            this.value = 'https://' + url;
        }
    });

    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const bio = bioTextarea.value.trim();
        if (bio.length > 500) {
            e.preventDefault();
            alert('Bio cannot exceed 500 characters');
            bioTextarea.focus();
        }
    });
});
</script>
{% endblock %}
