#!/usr/bin/env python
"""
Quick URL test script to verify all URLs are working.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'socialapp.settings')
django.setup()

from django.urls import reverse
from django.test import Client
from django.contrib.auth.models import User

def test_urls():
    """Test all main URLs to ensure they're accessible."""
    client = Client()
    
    # Test basic URLs (no authentication required)
    basic_urls = [
        ('home', []),
        ('register', []),
        ('login', []),
        ('search', []),
    ]
    
    print("Testing basic URLs (no auth required):")
    for url_name, args in basic_urls:
        try:
            url = reverse(url_name, args=args)
            response = client.get(url)
            status = "✓" if response.status_code in [200, 302] else "✗"
            print(f"  {status} {url_name}: {url} (Status: {response.status_code})")
        except Exception as e:
            print(f"  ✗ {url_name}: ERROR - {e}")
    
    # Create a test user for authenticated URLs
    try:
        user = User.objects.create_user(username='testuser', password='testpass123')
        client.login(username='testuser', password='testpass123')
        print(f"\n✓ Created test user and logged in")
    except Exception as e:
        print(f"\n✗ Failed to create test user: {e}")
        return
    
    # Test authenticated URLs
    auth_urls = [
        ('logout', []),
        ('edit_profile', []),
        ('create_post', []),
        ('bookmarks', []),
        ('notifications', []),
        ('profile', ['testuser']),
        ('follow_user', ['testuser']),
    ]
    
    print("\nTesting authenticated URLs:")
    for url_name, args in auth_urls:
        try:
            url = reverse(url_name, args=args)
            response = client.get(url)
            status = "✓" if response.status_code in [200, 302, 405] else "✗"  # 405 for POST-only views
            print(f"  {status} {url_name}: {url} (Status: {response.status_code})")
        except Exception as e:
            print(f"  ✗ {url_name}: ERROR - {e}")
    
    # Test URLs that require parameters (with dummy data)
    param_urls = [
        ('toggle_reaction', [1]),
        ('add_comment', [1]),
        ('toggle_bookmark', [1]),
        ('share_post', [1]),
        ('post_detail', [1]),
        ('edit_post', [1]),
        ('delete_post', [1]),
        ('hashtag_detail', ['test']),
    ]
    
    print("\nTesting parameterized URLs (may fail if objects don't exist):")
    for url_name, args in param_urls:
        try:
            url = reverse(url_name, args=args)
            response = client.get(url)
            status = "✓" if response.status_code in [200, 302, 404, 405] else "✗"  # 404 is OK for non-existent objects
            print(f"  {status} {url_name}: {url} (Status: {response.status_code})")
        except Exception as e:
            print(f"  ✗ {url_name}: ERROR - {e}")
    
    # Clean up
    user.delete()
    print(f"\n✓ Cleaned up test user")

if __name__ == '__main__':
    test_urls()
