{% extends 'base.html' %}
{% load static %}

{% block title %}Find Users to Chat - SocialApp{% endblock %}

{% block extra_css %}
<style>
    .search-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .search-box {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        padding: 30px;
        margin-bottom: 30px;
    }
    
    .search-input {
        width: 100%;
        padding: 15px 20px;
        border: 2px solid #dee2e6;
        border-radius: 25px;
        font-size: 16px;
        outline: none;
        transition: border-color 0.2s;
    }
    
    .search-input:focus {
        border-color: #007bff;
    }
    
    .search-results {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
    }
    
    .user-item {
        display: flex;
        align-items: center;
        padding: 20px;
        border-bottom: 1px solid #f8f9fa;
        text-decoration: none;
        color: inherit;
        transition: background-color 0.2s;
    }
    
    .user-item:last-child {
        border-bottom: none;
    }
    
    .user-item:hover {
        background-color: #f8f9fa;
        text-decoration: none;
        color: inherit;
    }
    
    .user-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: #007bff;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 24px;
        margin-right: 20px;
    }
    
    .user-info {
        flex: 1;
    }
    
    .user-name {
        font-weight: 600;
        font-size: 18px;
        margin-bottom: 5px;
    }
    
    .user-username {
        color: #6c757d;
        font-size: 14px;
    }
    
    .chat-btn {
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 20px;
        text-decoration: none;
        font-weight: 500;
        transition: background-color 0.2s;
    }
    
    .chat-btn:hover {
        background: #0056b3;
        color: white;
        text-decoration: none;
    }
    
    .no-results {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }
    
    .no-results i {
        font-size: 48px;
        margin-bottom: 20px;
    }
    
    .back-link {
        color: #007bff;
        text-decoration: none;
        margin-bottom: 20px;
        display: inline-block;
    }
    
    .back-link:hover {
        text-decoration: underline;
    }
</style>
{% endblock %}

{% block content %}
<div class="search-container">
    <a href="{% url 'chat_home' %}" class="back-link">
        <i class="fas fa-arrow-left"></i> Back to Chats
    </a>
    
    <div class="search-box">
        <h3 class="mb-4">Find Users to Chat With</h3>
        <form method="get" action="{% url 'chat_search' %}">
            <input 
                type="text" 
                name="q" 
                class="search-input" 
                placeholder="Search by username or name..." 
                value="{{ query }}"
                autofocus
            >
        </form>
    </div>
    
    {% if query %}
        <div class="search-results">
            {% if users %}
                {% for user in users %}
                    <div class="user-item">
                        <div class="user-avatar">
                            {{ user.username|first|upper }}
                        </div>
                        <div class="user-info">
                            <div class="user-name">
                                {{ user.get_full_name|default:user.username }}
                            </div>
                            <div class="user-username">
                                @{{ user.username }}
                            </div>
                        </div>
                        <a href="{% url 'start_chat' username=user.username %}" class="chat-btn">
                            <i class="fas fa-comment"></i> Chat
                        </a>
                    </div>
                {% endfor %}
            {% else %}
                <div class="no-results">
                    <i class="fas fa-search"></i>
                    <h5>No users found</h5>
                    <p>Try searching with a different term.</p>
                </div>
            {% endif %}
        </div>
    {% else %}
        <div class="search-results">
            <div class="no-results">
                <i class="fas fa-users"></i>
                <h5>Search for Users</h5>
                <p>Enter a username or name to find people to chat with.</p>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-submit search form on input
    const searchInput = document.querySelector('.search-input');
    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            if (searchInput.value.trim().length > 0) {
                searchInput.closest('form').submit();
            }
        }, 500);
    });
</script>
{% endblock %}
