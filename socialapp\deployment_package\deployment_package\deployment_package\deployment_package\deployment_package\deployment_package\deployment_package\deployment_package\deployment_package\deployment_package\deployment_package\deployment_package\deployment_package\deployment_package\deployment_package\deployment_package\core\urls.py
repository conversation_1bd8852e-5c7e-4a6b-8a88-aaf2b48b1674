from django.urls import path
from django.contrib.auth import views as auth_views
from . import views

urlpatterns = [
    path('', views.home, name='home'),
    path('register/', views.register, name='register'),
    path('login/', auth_views.LoginView.as_view(template_name='login.html'), name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('search/', views.search, name='search'),
]

urlpatterns += [
    path('profile/<str:username>/', views.profile, name='profile'),
    path('profile/edit/', views.edit_profile, name='edit_profile'),
    path('follow/<str:username>/', views.follow_user, name='follow_user'),
]

urlpatterns += [
    path('post/new/', views.create_post, name='create_post'),
    path('post/<int:pk>/', views.post_detail, name='post_detail'),
    path('post/<int:pk>/edit/', views.edit_post, name='edit_post'),
    path('post/<int:pk>/delete/', views.delete_post, name='delete_post'),
    path('post/<int:post_pk>/comment/', views.add_comment, name='add_comment'),
    path('post/<int:post_pk>/react/', views.toggle_reaction, name='toggle_reaction'),
    path('post/<int:post_pk>/bookmark/', views.toggle_bookmark, name='toggle_bookmark'),
    path('post/<int:post_pk>/share/', views.share_post, name='share_post'),
]

urlpatterns += [
    path('hashtag/<slug:slug>/', views.hashtag_detail, name='hashtag_detail'),
]

urlpatterns += [
    path('bookmarks/', views.bookmarks, name='bookmarks'),
    path('notifications/', views.notifications, name='notifications'),
    path('notifications/mark-read/<int:notification_id>/', views.mark_notification_read, name='mark_read'),
    path('notifications/mark-all-read/', views.mark_all_notifications_read, name='mark_all_read'),
]

# Chat URLs
urlpatterns += [
    path('chat/', views.chat_home, name='chat_home'),
    path('chat/room/<uuid:room_id>/', views.chat_room, name='chat_room'),
    path('chat/start/<str:username>/', views.start_chat, name='start_chat'),
    path('chat/create-group/', views.create_group_chat, name='create_group_chat'),
    path('chat/search/', views.chat_search, name='chat_search'),

    # Chat API endpoints
    path('api/chat/<uuid:room_id>/send/', views.send_message_api, name='send_message_api'),
]

