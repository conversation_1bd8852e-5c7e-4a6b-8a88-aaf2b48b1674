# type: ignore
"""
Security tests for the social blogging platform.
"""

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse
from django.conf import settings
from core.models import Profile, Post
from core.forms import CustomUserCreationForm, PostForm
import tempfile
import os


class SecurityTestCase(TestCase):
    """Test security features of the application."""
    
    def setUp(self):
        """Set up test data."""
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.profile = Profile.objects.create(user=self.user)
    
    def test_csrf_protection(self):
        """Test CSRF protection on forms."""
        # Test login without CSRF token
        response = self.client.post('/login/', {
            'username': 'testuser',
            'password': 'testpass123'
        })
        # Should be rejected due to missing CSRF token
        self.assertEqual(response.status_code, 403)
    
    def test_file_upload_validation(self):
        """Test file upload security."""
        self.client.login(username='testuser', password='testpass123')
        
        # Test malicious file upload
        malicious_file = SimpleUploadedFile(
            "malicious.php",
            b"<?php echo 'hacked'; ?>",
            content_type="application/x-php"
        )
        
        response = self.client.post(reverse('create_post'), {
            'content': 'Test post',
            'image': malicious_file
        })
        
        # Should reject malicious file
        self.assertContains(response, 'Only JPEG, PNG, GIF, and WebP images are allowed.')
    
    def test_large_file_upload(self):
        """Test large file upload rejection."""
        self.client.login(username='testuser', password='testpass123')
        
        # Create a large file (simulate 6MB)
        large_content = b'x' * (6 * 1024 * 1024)
        large_file = SimpleUploadedFile(
            "large.jpg",
            large_content,
            content_type="image/jpeg"
        )
        
        response = self.client.post(reverse('create_post'), {
            'content': 'Test post',
            'image': large_file
        })
        
        # Should reject large file
        self.assertContains(response, 'Image file size must be less than 5MB.')
    
    def test_xss_prevention(self):
        """Test XSS prevention in forms."""
        self.client.login(username='testuser', password='testpass123')
        
        # Try to inject script
        malicious_content = '<script>alert("xss")</script>Hello world'
        
        response = self.client.post(reverse('create_post'), {
            'content': malicious_content,
            'is_public': True
        })
        
        # Check that script tags are stripped
        if response.status_code == 302:  # Successful post creation
            post = Post.objects.filter(user=self.user).first()
            self.assertNotIn('<script>', post.content)
            self.assertIn('Hello world', post.content)
    
    def test_sql_injection_prevention(self):
        """Test SQL injection prevention in search."""
        # Try SQL injection in search
        malicious_query = "'; DROP TABLE core_post; --"
        
        response = self.client.get(reverse('search'), {
            'q': malicious_query
        })
        
        # Should not cause error and should escape the query
        self.assertEqual(response.status_code, 200)
        # Posts table should still exist
        self.assertTrue(Post.objects.all().exists() or True)  # Table exists even if empty
    
    def test_user_registration_validation(self):
        """Test user registration form validation."""
        # Test duplicate email
        form_data = {
            'username': 'newuser',
            'email': '<EMAIL>',  # Already exists
            'password1': 'newpass123',
            'password2': 'newpass123'
        }
        
        form = CustomUserCreationForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('A user with this email already exists.', str(form.errors))
    
    def test_username_validation(self):
        """Test username format validation."""
        # Test invalid username with special characters
        form_data = {
            'username': 'user@name!',
            'email': '<EMAIL>',
            'password1': 'newpass123',
            'password2': 'newpass123'
        }
        
        form = CustomUserCreationForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('Username can only contain letters, numbers, and underscores.', str(form.errors))
    
    def test_post_content_validation(self):
        """Test post content validation."""
        # Test empty content
        form = PostForm(data={'content': '', 'is_public': True})
        self.assertFalse(form.is_valid())
        self.assertIn('Post content cannot be empty.', str(form.errors))
        
        # Test very long content
        long_content = 'x' * 10001
        form = PostForm(data={'content': long_content, 'is_public': True})
        self.assertFalse(form.is_valid())
        self.assertIn('Post cannot exceed 10,000 characters.', str(form.errors))
    
    def test_authentication_required(self):
        """Test that authentication is required for protected views."""
        # Test creating post without login
        response = self.client.get(reverse('create_post'))
        self.assertEqual(response.status_code, 302)  # Redirect to login
        
        # Test following user without login
        response = self.client.post(reverse('toggle_follow', args=[self.user.pk]))
        self.assertEqual(response.status_code, 302)  # Redirect to login
    
    def test_permission_checks(self):
        """Test permission checks for user actions."""
        # Create another user
        other_user = User.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='otherpass123'
        )
        
        # Create a post by other user
        post = Post.objects.create(
            user=other_user,
            content='Other user post'
        )
        
        # Login as first user
        self.client.login(username='testuser', password='testpass123')
        
        # Try to edit other user's post
        response = self.client.get(reverse('edit_post', args=[post.pk]))
        self.assertEqual(response.status_code, 403)  # Forbidden
        
        # Try to delete other user's post
        response = self.client.post(reverse('delete_post', args=[post.pk]))
        self.assertEqual(response.status_code, 403)  # Forbidden
    
    def test_security_headers(self):
        """Test that security headers are present."""
        response = self.client.get('/')
        
        # Check for security headers
        self.assertEqual(response.get('X-Frame-Options'), 'DENY')
        self.assertEqual(response.get('X-Content-Type-Options'), 'nosniff')
        self.assertEqual(response.get('X-XSS-Protection'), '1; mode=block')
        self.assertEqual(response.get('Referrer-Policy'), 'strict-origin-when-cross-origin')
    
    def test_rate_limiting_simulation(self):
        """Test rate limiting (simulation)."""
        # This would require actual rate limiting to be enabled
        # For now, just test that the view responds normally
        response = self.client.get('/')
        self.assertEqual(response.status_code, 200)
    
    def tearDown(self):
        """Clean up test data."""
        # Clean up any uploaded files
        if hasattr(settings, 'MEDIA_ROOT'):
            import shutil
            test_media_path = os.path.join(settings.MEDIA_ROOT, 'test')
            if os.path.exists(test_media_path):
                shutil.rmtree(test_media_path)
