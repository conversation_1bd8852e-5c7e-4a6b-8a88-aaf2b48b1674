# Production Requirements for Django SocialApp
# Core Django
Django>=5.2.3,<5.3
django-environ>=0.11.2

# Database
psycopg2-binary>=2.9.9  # PostgreSQL for production

# Image Processing
Pillow>=10.1.0
django-imagekit>=5.0.0

# Security
django-cors-headers>=4.3.1
django-ratelimit>=4.1.0

# Real-time Features
channels>=4.0.0
channels-redis>=4.1.0

# Production Server
gunicorn>=21.2.0
whitenoise>=6.6.0
redis>=5.0.1

# Monitoring & Logging
sentry-sdk>=1.38.0

# Environment
python-decouple>=3.8
