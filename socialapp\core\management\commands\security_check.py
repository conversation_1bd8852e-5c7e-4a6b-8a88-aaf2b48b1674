# type: ignore
"""
Django management command to perform security checks on the social app.
"""

from django.core.management.base import BaseCommand
from django.conf import settings
from django.contrib.auth.models import User
from django.db import connection
from core.models import Profile, Post, Comment
import os


class Command(BaseCommand):
    """Security check management command."""
    
    help = 'Perform security checks on the social app'

    def add_arguments(self, parser):
        """Add command arguments."""
        parser.add_argument(
            '--fix',
            action='store_true',
            help='Attempt to fix security issues automatically',
        )

    def handle(self, *args, **options):
        """Execute the security check."""
        self.stdout.write(
            self.style.SUCCESS('Starting security check...')
        )
        
        issues_found = 0
        
        # Check 1: Debug mode in production
        if settings.DEBUG and os.getenv('DJANGO_ENV') == 'production':
            self.stdout.write(
                self.style.ERROR('❌ DEBUG is True in production environment')
            )
            issues_found += 1
        else:
            self.stdout.write(
                self.style.SUCCESS('✅ DEBUG setting is appropriate')
            )
        
        # Check 2: Secret key security
        if settings.SECRET_KEY == 'your-secret-key-here' or len(settings.SECRET_KEY) < 50:
            self.stdout.write(
                self.style.ERROR('❌ SECRET_KEY is weak or default')
            )
            issues_found += 1
        else:
            self.stdout.write(
                self.style.SUCCESS('✅ SECRET_KEY appears secure')
            )
        
        # Check 3: Database security
        if 'sqlite3' in settings.DATABASES['default']['ENGINE'] and os.getenv('DJANGO_ENV') == 'production':
            self.stdout.write(
                self.style.WARNING('⚠️  Using SQLite in production (consider PostgreSQL)')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS('✅ Database configuration is appropriate')
            )
        
        # Check 4: HTTPS settings
        if not settings.SECURE_SSL_REDIRECT and os.getenv('DJANGO_ENV') == 'production':
            self.stdout.write(
                self.style.ERROR('❌ HTTPS redirect not enabled in production')
            )
            issues_found += 1
        else:
            self.stdout.write(
                self.style.SUCCESS('✅ HTTPS settings configured')
            )
        
        # Check 5: User data validation
        users_without_profiles = User.objects.filter(profile__isnull=True).count()
        if users_without_profiles > 0:
            self.stdout.write(
                self.style.WARNING(f'⚠️  {users_without_profiles} users without profiles')
            )
            if options['fix']:
                # Create missing profiles
                for user in User.objects.filter(profile__isnull=True):
                    Profile.objects.create(user=user)
                self.stdout.write(
                    self.style.SUCCESS(f'✅ Created {users_without_profiles} missing profiles')
                )
        else:
            self.stdout.write(
                self.style.SUCCESS('✅ All users have profiles')
            )
        
        # Check 6: File upload security
        media_root = settings.MEDIA_ROOT
        if os.path.exists(media_root):
            # Check for executable files in media directory
            executable_files = []
            for root, dirs, files in os.walk(media_root):
                for file in files:
                    if file.endswith(('.php', '.py', '.js', '.sh', '.exe')):
                        executable_files.append(os.path.join(root, file))
            
            if executable_files:
                self.stdout.write(
                    self.style.ERROR(f'❌ Found {len(executable_files)} potentially dangerous files in media directory')
                )
                for file in executable_files[:5]:  # Show first 5
                    self.stdout.write(f'   - {file}')
                issues_found += 1
            else:
                self.stdout.write(
                    self.style.SUCCESS('✅ No dangerous files found in media directory')
                )
        
        # Check 7: Database queries optimization
        with connection.cursor() as cursor:
            # Check for posts without proper indexes
            cursor.execute("""
                SELECT COUNT(*) FROM core_post 
                WHERE created_at > datetime('now', '-1 day')
            """)
            recent_posts = cursor.fetchone()[0]
            
            if recent_posts > 1000:
                self.stdout.write(
                    self.style.WARNING('⚠️  High volume of recent posts - consider database optimization')
                )
            else:
                self.stdout.write(
                    self.style.SUCCESS('✅ Post volume is manageable')
                )
        
        # Check 8: Content validation
        posts_with_long_content = Post.objects.filter(content__length__gt=10000).count()
        if posts_with_long_content > 0:
            self.stdout.write(
                self.style.WARNING(f'⚠️  {posts_with_long_content} posts exceed recommended length')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS('✅ All posts are within length limits')
            )
        
        # Summary
        self.stdout.write('\n' + '='*50)
        if issues_found == 0:
            self.stdout.write(
                self.style.SUCCESS('🎉 No critical security issues found!')
            )
        else:
            self.stdout.write(
                self.style.ERROR(f'⚠️  Found {issues_found} security issues that need attention')
            )
        
        self.stdout.write(
            self.style.SUCCESS('Security check completed.')
        )
