{% extends 'base.html' %}

{% block title %}Search - BlogApp{% endblock %}

{% block content %}
<div class="mb-4">
    <h2><i class="fas fa-search"></i> Search Results</h2>
    {% if query %}
    <p class="text-muted">Showing results for "{{ query }}"</p>
    {% endif %}
</div>

<!-- Search Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{% url 'search' %}">
            <div class="row">
                <div class="col-md-6">
                    <input type="text" class="form-control" name="q" value="{{ query }}" 
                           placeholder="Search posts, users, or hashtags...">
                </div>
                <div class="col-md-3">
                    <select class="form-select" name="type">
                        <option value="all" {% if search_type == 'all' %}selected{% endif %}>All</option>
                        <option value="posts" {% if search_type == 'posts' %}selected{% endif %}>Posts</option>
                        <option value="users" {% if search_type == 'users' %}selected{% endif %}>Users</option>
                        <option value="hashtags" {% if search_type == 'hashtags' %}selected{% endif %}>Hashtags</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i> Search
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

{% if query %}
    {% if posts or users or hashtags %}
        <!-- Search Results -->
        
        <!-- Posts Results -->
        {% if posts %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-alt"></i> Posts ({{ posts.paginator.count }})
                </h5>
            </div>
            <div class="card-body p-0">
                {% for post in posts %}
                <div class="border-bottom p-3">
                    <div class="d-flex align-items-start">
                        <img src="{{ post.user.profile.get_avatar_url }}" 
                             alt="{{ post.user.username }}" class="avatar-sm me-3">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-1">
                                <h6 class="mb-0 fw-bold">{{ post.user.username }}</h6>
                                {% if post.user.profile.is_verified %}
                                <i class="fas fa-check-circle text-primary ms-1" title="Verified"></i>
                                {% endif %}
                                <small class="text-muted ms-2">
                                    {{ post.created_at|timesince }} ago
                                    {% if not post.is_public %}
                                    <i class="fas fa-lock ms-1" title="Private post"></i>
                                    {% endif %}
                                </small>
                            </div>
                            
                            <p class="mb-2">{{ post.content|truncatechars:200 }}</p>
                            
                            {% if post.hashtags.all %}
                            <div class="mb-2">
                                {% for hashtag in post.hashtags.all %}
                                <a href="{% url 'hashtag_detail' hashtag.slug %}" class="hashtag me-1">
                                    #{{ hashtag.name }}
                                </a>
                                {% endfor %}
                            </div>
                            {% endif %}
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="text-muted small">
                                    <span><i class="fas fa-heart"></i> {{ post.likes_count }}</span>
                                    <span class="mx-2">•</span>
                                    <span><i class="fas fa-comment"></i> {{ post.comments_count }}</span>
                                    <span class="mx-2">•</span>
                                    <span><i class="fas fa-eye"></i> {{ post.views_count }}</span>
                                </div>
                                <a href="{% url 'post_detail' post.pk %}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i> View
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        <!-- Users Results -->
        {% if users %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users"></i> Users ({{ users.paginator.count }})
                </h5>
            </div>
            <div class="card-body p-0">
                {% for user_result in users %}
                <div class="border-bottom p-3">
                    <div class="d-flex align-items-center">
                        <img src="{{ user_result.profile.get_avatar_url }}" 
                             alt="{{ user_result.username }}" class="avatar me-3">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-1">
                                <h6 class="mb-0 fw-bold">{{ user_result.username }}</h6>
                                {% if user_result.profile.is_verified %}
                                <i class="fas fa-check-circle text-primary ms-1" title="Verified"></i>
                                {% endif %}
                            </div>
                            
                            {% if user_result.profile.bio %}
                            <p class="text-muted mb-2">{{ user_result.profile.bio|truncatechars:100 }}</p>
                            {% endif %}
                            
                            <div class="d-flex align-items-center gap-3 text-muted small">
                                <span><i class="fas fa-file-alt"></i> {{ user_result.profile.posts_count }} posts</span>
                                <span><i class="fas fa-user-friends"></i> {{ user_result.profile.followers_count }} followers</span>
                                <span><i class="fas fa-calendar"></i> Joined {{ user_result.date_joined|date:"M Y" }}</span>
                            </div>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="{% url 'profile' user_result.username %}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-user"></i> View Profile
                            </a>
                            {% if user.is_authenticated and user != user_result %}
                            <a href="{% url 'follow_user' user_result.username %}" 
                               class="btn btn-sm {% if user_result in user.profile.following.all %}btn-outline-secondary{% else %}btn-primary{% endif %}">
                                {% if user_result in user.profile.following.all %}
                                <i class="fas fa-user-minus"></i> Unfollow
                                {% else %}
                                <i class="fas fa-user-plus"></i> Follow
                                {% endif %}
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        <!-- Hashtags Results -->
        {% if hashtags %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-hashtag"></i> Hashtags ({{ hashtags.paginator.count }})
                </h5>
            </div>
            <div class="card-body p-0">
                {% for hashtag in hashtags %}
                <div class="border-bottom p-3">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h6 class="mb-1">
                                <a href="{% url 'hashtag_detail' hashtag.slug %}" class="hashtag text-decoration-none">
                                    #{{ hashtag.name }}
                                </a>
                            </h6>
                            <div class="text-muted small">
                                <span><i class="fas fa-file-alt"></i> {{ hashtag.posts_count }} posts</span>
                                <span class="mx-2">•</span>
                                <span><i class="fas fa-users"></i> {{ hashtag.followers_count }} followers</span>
                            </div>
                        </div>
                        <a href="{% url 'hashtag_detail' hashtag.slug %}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye"></i> View Posts
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        <!-- Pagination -->
        {% if posts.has_other_pages or users.has_other_pages or hashtags.has_other_pages %}
        <nav aria-label="Search results pagination">
            <ul class="pagination justify-content-center">
                {% if posts.has_previous or users.has_previous or hashtags.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?q={{ query }}&type={{ search_type }}&page={{ previous_page }}">
                        <i class="fas fa-chevron-left"></i> Previous
                    </a>
                </li>
                {% endif %}

                {% for num in page_range %}
                    {% if current_page == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% else %}
                    <li class="page-item">
                        <a class="page-link" href="?q={{ query }}&type={{ search_type }}&page={{ num }}">{{ num }}</a>
                    </li>
                    {% endif %}
                {% endfor %}

                {% if posts.has_next or users.has_next or hashtags.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?q={{ query }}&type={{ search_type }}&page={{ next_page }}">
                        Next <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
    {% else %}
        <!-- No Results -->
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h5>No results found</h5>
                <p class="text-muted">
                    We couldn't find any results for "{{ query }}". Try different keywords or check your spelling.
                </p>
                <div class="d-flex justify-content-center gap-2">
                    <a href="{% url 'home' %}" class="btn btn-primary">
                        <i class="fas fa-home"></i> Go to Home
                    </a>
                    <button onclick="history.back()" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Go Back
                    </button>
                </div>
            </div>
        </div>
    {% endif %}
{% else %}
    <!-- Search Suggestions -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-lightbulb"></i> Search Tips</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <h6><i class="fas fa-file-alt text-primary"></i> Search Posts</h6>
                    <p class="text-muted small">Find posts by keywords, content, or hashtags</p>
                </div>
                <div class="col-md-4">
                    <h6><i class="fas fa-users text-success"></i> Find Users</h6>
                    <p class="text-muted small">Discover new people to follow</p>
                </div>
                <div class="col-md-4">
                    <h6><i class="fas fa-hashtag text-info"></i> Explore Hashtags</h6>
                    <p class="text-muted small">Find trending topics and discussions</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Trending Searches -->
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-fire"></i> Trending Searches</h5>
        </div>
        <div class="card-body">
            <div class="d-flex flex-wrap gap-2">
                {% for hashtag in trending_hashtags %}
                <a href="{% url 'search' %}?q={{ hashtag.name }}&type=hashtags" 
                   class="btn btn-outline-primary btn-sm">
                    #{{ hashtag.name }}
                </a>
                {% endfor %}
            </div>
        </div>
    </div>
{% endif %}
{% endblock %} 