# type: ignore
"""
Views for the blogging platform.

This module contains all the view functions and classes for handling
user interactions, post management, and social features.
"""

from django.http import HttpResponseForbidden, Http404, JsonResponse
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count, Prefetch
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.urls import reverse_lazy
from django.contrib.auth import login, logout
from django.core.exceptions import PermissionDenied, ValidationError
from django.http import HttpResponseRedirect
from django.utils import timezone
from datetime import timedelta
import re
from django.utils.text import slugify
from django.utils.html import escape
from django_ratelimit.decorators import ratelimit
from django.core.cache import cache
import logging

logger = logging.getLogger(__name__)

from .models import Profile, Post, Comment, Reaction, Hashtag, Follow, Bookmark, Share, Notification


def home(request):
    """
    Home page view (Feed).
    
    Shows posts from followed users and trending content.
    """
    if not request.user.is_authenticated and request.GET.get('next'):
        messages.info(request, "You need to log in to access that page.")
    
    if request.user.is_authenticated:
        # Get posts from followed users and user's own posts
        followed_users = request.user.following.values_list('following_id', flat=True)
        posts = Post.objects.filter(
            Q(user__in=followed_users) | Q(user=request.user),
            is_public=True,
            is_deleted=False
        ).select_related('user', 'user__profile').prefetch_related(
            'hashtags', 'reactions', 'comments'
        ).order_by('-created_at')
    else:
        # Show trending posts for anonymous users
        posts = Post.objects.filter(
            is_public=True,
            is_deleted=False
        ).select_related('user', 'user__profile').prefetch_related(
            'hashtags', 'reactions', 'comments'
        ).order_by('-likes_count', '-created_at')
    
    # Pagination
    paginator = Paginator(posts, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'posts': page_obj,
        'trending_hashtags': Hashtag.objects.filter(posts_count__gt=0).order_by('-posts_count')[:10],
        'suggested_users': User.objects.exclude(
            id__in=request.user.following.values_list('following_id', flat=True) if request.user.is_authenticated else []
        ).exclude(id=request.user.id if request.user.is_authenticated else 0)[:5],
    }
    
    return render(request, 'home.html', context)


@ratelimit(key='ip', rate='5/h', method='POST', block=True)
def register(request):
    """
    User registration view.
    
    Handles user account creation with automatic login.
    """
    if request.user.is_authenticated:
        return redirect('home')
    
    if request.method == 'POST':
        form = UserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            # Auto-login after registration
            login(request, user)
            messages.success(request, f"Welcome to BlogApp, {user.username}!")
            return redirect('home')
        else:
            messages.error(request, "Please correct the errors below.")
    else:
        form = UserCreationForm()
    
    return render(request, 'register.html', {'form': form})


def logout_view(request):
    """
    Custom logout view.
    Logs out the user and redirects to the login page.
    """
    if request.user.is_authenticated:
        logout(request)
        messages.success(request, "You have been successfully logged out.")
    return redirect('login')


def profile(request, username):
    """
    User profile view.
    
    Displays user profile information and their posts.
    """
    profile_user = get_object_or_404(User, username=username)
    
    # Get user's posts
    posts = Post.objects.filter(
        user=profile_user,
        is_deleted=False
    ).select_related('user').prefetch_related('hashtags', 'reactions', 'comments').order_by('-created_at')
    
    # Paginate posts
    paginator = Paginator(posts, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Check if current user is following this user
    is_following = False
    if request.user.is_authenticated and request.user != profile_user:
        is_following = Follow.objects.filter(
            follower=request.user,
            following=profile_user
        ).exists()
    
    context = {
        'profile_user': profile_user,
        'posts': page_obj,
        'posts_count': posts.count(),
        'followers_count': profile_user.profile.followers_count,
        'following_count': profile_user.profile.following_count,
        'is_following': is_following,
    }
    
    return render(request, 'profile.html', context)


@login_required
def edit_profile(request):
    """
    Profile editing view.
    
    Allows users to update their profile information.
    """
    if request.method == 'POST':
        bio = request.POST.get('bio', '').strip()
        avatar = request.FILES.get('avatar')
        cover_photo = request.FILES.get('cover_photo')
        location = request.POST.get('location', '').strip()
        website = request.POST.get('website', '').strip()
        
        profile = request.user.profile
        
        # Validate bio length
        if len(bio) > 500:
            messages.error(request, "Bio cannot exceed 500 characters.")
            return render(request, 'edit_profile.html')
        
        # Validate website URL
        if website and not website.startswith(('http://', 'https://')):
            website = 'https://' + website
        
        profile.bio = bio
        profile.location = location
        profile.website = website
        
        if avatar:
            # Validate file type and size
            if avatar.size > 5 * 1024 * 1024:  # 5MB limit
                messages.error(request, "Avatar file size must be less than 5MB.")
                return render(request, 'edit_profile.html')
            
            allowed_types = ['image/jpeg', 'image/png', 'image/gif']
            if avatar.content_type not in allowed_types:
                messages.error(request, "Avatar must be a JPEG, PNG, or GIF image.")
                return render(request, 'edit_profile.html')
            
            profile.avatar = avatar
        
        if cover_photo:
            # Validate file type and size
            if cover_photo.size > 10 * 1024 * 1024:  # 10MB limit
                messages.error(request, "Cover photo file size must be less than 10MB.")
                return render(request, 'edit_profile.html')
            
            allowed_types = ['image/jpeg', 'image/png', 'image/gif']
            if cover_photo.content_type not in allowed_types:
                messages.error(request, "Cover photo must be a JPEG, PNG, or GIF image.")
                return render(request, 'edit_profile.html')
            
            profile.cover_photo = cover_photo
        
        profile.save()
        messages.success(request, "Profile updated successfully!")
        return redirect('profile', username=request.user.username)
    
    return render(request, 'edit_profile.html')


@login_required
@ratelimit(key='user', rate='10/h', method='POST', block=True)
def create_post(request):
    """
    Post creation view.
    
    Allows authenticated users to create new posts.
    """
    if request.method == 'POST':
        content = request.POST.get('content', '').strip()
        image = request.FILES.get('image')
        video = request.FILES.get('video')
        is_public = request.POST.get('is_public') == 'on'
        
        # Validation
        if not content:
            messages.error(request, "Post content cannot be empty.")
            return render(request, 'posts/create.html', {
                'trending_hashtags': Hashtag.objects.filter(posts_count__gt=0).order_by('-posts_count')[:10]
            })
        
        if len(content) > 10000:
            messages.error(request, "Post cannot exceed 10000 characters.")
            return render(request, 'posts/create.html', {
                'trending_hashtags': Hashtag.objects.filter(posts_count__gt=0).order_by('-posts_count')[:10]
            })
        
        # Validate file uploads
        if image and image.size > 10 * 1024 * 1024:  # 10MB limit
            messages.error(request, "Image file size must be less than 10MB.")
            return render(request, 'posts/create.html', {
                'trending_hashtags': Hashtag.objects.filter(posts_count__gt=0).order_by('-posts_count')[:10]
            })
        
        if video and video.size > 100 * 1024 * 1024:  # 100MB limit
            messages.error(request, "Video file size must be less than 100MB.")
            return render(request, 'posts/create.html', {
                'trending_hashtags': Hashtag.objects.filter(posts_count__gt=0).order_by('-posts_count')[:10]
            })
        
        # Create post
        post = Post.objects.create(
            user=request.user,
            content=content,
            image=image,
            video=video,
            is_public=is_public
        )
        
        # Process hashtags from content
        hashtag_pattern = r'#(\w+)'
        hashtags = re.findall(hashtag_pattern, content)
        
        for hashtag_name in hashtags:
            hashtag, _ = Hashtag.objects.get_or_create(
                name=hashtag_name.lower(),
                defaults={'slug': slugify(hashtag_name.lower())}
            )
            post.hashtags.add(hashtag)
        
        messages.success(request, "Post created successfully!")
        return redirect('post_detail', pk=post.pk)
    
    return render(request, 'posts/create.html', {
        'trending_hashtags': Hashtag.objects.filter(posts_count__gt=0).order_by('-posts_count')[:10]
    })


def post_detail(request, pk):
    """
    Post detail view.
    
    Shows a single post with comments and reactions.
    """
    post = get_object_or_404(Post, pk=pk, is_deleted=False)
    
    # Increment view count
    post.views_count += 1
    post.save(update_fields=['views_count'])
    
    # Get comments for this post
    comments = Comment.objects.filter(
        post=post,
        parent=None,  # Only top-level comments
        is_deleted=False
    ).select_related('user', 'user__profile').prefetch_related('replies', 'reactions').order_by('-created_at')
    
    # Paginate comments
    paginator = Paginator(comments, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'post': post,
        'comments': page_obj,
        'total_comments': comments.count(),
        'is_liked': post.is_liked_by(request.user) if request.user.is_authenticated else False,
        'is_bookmarked': post.is_bookmarked_by(request.user) if request.user.is_authenticated else False,
    }
    
    return render(request, 'posts/detail.html', context)


@login_required
def edit_post(request, pk):
    """
    Post editing view.
    
    Allows users to edit their own posts.
    """
    post = get_object_or_404(Post, pk=pk, is_deleted=False)
    
    # Only the author can edit
    if post.user != request.user:
        raise PermissionDenied("You can only edit your own posts.")
    
    if request.method == 'POST':
        content = request.POST.get('content', '').strip()
        image = request.FILES.get('image')
        video = request.FILES.get('video')
        is_public = request.POST.get('is_public') == 'on'
        
        # Validation
        if not content:
            messages.error(request, "Post content cannot be empty.")
            return render(request, 'posts/edit.html', {'post': post})
        
        if len(content) > 10000:
            messages.error(request, "Post cannot exceed 10000 characters.")
            return render(request, 'posts/edit.html', {'post': post})
        
        # Update post
        post.content = content
        post.is_public = is_public
        
        if image:
            post.image = image
        if video:
            post.video = video
        
        post.save()
        
        messages.success(request, "Post updated successfully!")
        return redirect('post_detail', pk=post.pk)
    
    return render(request, 'posts/edit.html', {'post': post})


@login_required
def delete_post(request, pk):
    """
    Post deletion view.
    
    Allows users to delete their own posts.
    """
    post = get_object_or_404(Post, pk=pk, is_deleted=False)
    
    # Only the author can delete
    if post.user != request.user:
        raise PermissionDenied("You can only delete your own posts.")
    
    if request.method == 'POST':
        post.soft_delete()
        messages.success(request, "Post deleted successfully!")
        return redirect('home')
    
    return render(request, 'posts/confirm_delete.html', {'post': post})


@login_required
@ratelimit(key='user', rate='30/h', method='POST', block=True)
@require_http_methods(["POST"])
def add_comment(request, post_pk):
    """
    Comment creation view.
    
    Allows users to comment on posts.
    """
    post = get_object_or_404(Post, pk=post_pk, is_deleted=False)
    
    content = request.POST.get('content', '').strip()
    parent_id = request.POST.get('parent')
    
    # Validation
    if not content:
        messages.error(request, "Comment cannot be empty.")
        return redirect('post_detail', pk=post_pk)
    
    if len(content) > 1000:
        messages.error(request, "Comment cannot exceed 1000 characters.")
        return redirect('post_detail', pk=post_pk)
    
    # Get parent comment if this is a reply
    parent = None
    if parent_id:
        parent = get_object_or_404(Comment, id=parent_id, post=post, is_deleted=False)
    
    # Create comment
    comment = Comment.objects.create(
        post=post,
        user=request.user,
        content=content,
        parent=parent
    )
    
    # Create notification for post author
    if post.user != request.user:
        Notification.objects.create(
            recipient=post.user,
            sender=request.user,
            notification_type='comment',
            post=post,
            comment=comment
        )
    
    messages.success(request, "Comment added successfully!")
    return redirect('post_detail', pk=post_pk)


@login_required
@require_http_methods(["POST"])
def toggle_reaction(request, post_pk):
    """
    Reaction toggle view.
    
    Allows users to react to posts.
    """
    post = get_object_or_404(Post, pk=post_pk, is_deleted=False)
    reaction_type = request.POST.get('reaction_type', 'like')
    
    # Validate reaction type
    valid_reactions = ['like', 'love', 'laugh', 'wow', 'sad', 'angry']
    if reaction_type not in valid_reactions:
        messages.error(request, "Invalid reaction type.")
        return redirect('post_detail', pk=post_pk)
    
    # Toggle reaction
    reaction, created = Reaction.objects.get_or_create(
        user=request.user,
        post=post,
        defaults={'reaction_type': reaction_type}
    )
    
    if not created:
        if reaction.reaction_type == reaction_type:
            # Remove reaction if same type
            reaction.delete()
            messages.info(request, "Reaction removed.")
        else:
            # Update reaction type
            reaction.reaction_type = reaction_type
            reaction.save()
            messages.success(request, f"Reaction updated to {reaction_type}!")
    else:
        # Create notification for post author
        if post.user != request.user:
            Notification.objects.create(
                recipient=post.user,
                sender=request.user,
                notification_type='like',
                post=post
            )
        messages.success(request, f"Post {reaction_type}d!")
    
    return redirect('post_detail', pk=post_pk)


@login_required
@require_http_methods(["POST"])
def toggle_bookmark(request, post_pk):
    """
    Bookmark toggle view.
    
    Allows users to bookmark posts.
    """
    post = get_object_or_404(Post, pk=post_pk, is_deleted=False)
    
    # Toggle bookmark
    bookmark, created = Bookmark.objects.get_or_create(user=request.user, post=post)
    
    if not created:
        bookmark.delete()
        messages.info(request, "Post removed from bookmarks.")
    else:
        messages.success(request, "Post bookmarked!")
    
    return redirect('post_detail', pk=post_pk)


@login_required
@require_http_methods(["POST"])
def share_post(request, post_pk):
    """
    Share post view.
    
    Allows users to share posts.
    """
    original_post = get_object_or_404(Post, pk=post_pk, is_deleted=False)
    share_text = request.POST.get('share_text', '').strip()
    
    # Create share
    Share.objects.create(
        user=request.user,
        original_post=original_post,
        share_text=share_text
    )
    
    # Create notification for original post author
    if original_post.user != request.user:
        Notification.objects.create(
            recipient=original_post.user,
            sender=request.user,
            notification_type='share',
            post=original_post
        )
    
    messages.success(request, "Post shared successfully!")
    return redirect('post_detail', pk=original_post.pk)


@login_required
def follow_user(request, username):
    """
    Follow user view.
    
    Allows users to follow other users.
    """
    user_to_follow = get_object_or_404(User, username=username)
    
    if user_to_follow == request.user:
        messages.error(request, "You cannot follow yourself.")
        return redirect('profile', username=username)
    
    # Toggle follow
    follow, created = Follow.objects.get_or_create(
        follower=request.user,
        following=user_to_follow
    )
    
    if not created:
        follow.delete()
        messages.info(request, f"You unfollowed {user_to_follow.username}.")
    else:
        # Create notification
        Notification.objects.create(
            recipient=user_to_follow,
            sender=request.user,
            notification_type='follow'
        )
        messages.success(request, f"You are now following {user_to_follow.username}!")
    
    return redirect('profile', username=username)


def hashtag_detail(request, slug):
    """
    Hashtag detail view.
    
    Shows all posts with a specific hashtag.
    """
    hashtag = get_object_or_404(Hashtag, slug=slug)
    
    # Get posts with this hashtag
    posts = Post.objects.filter(
        hashtags=hashtag,
        is_public=True,
        is_deleted=False
    ).select_related('user', 'user__profile').prefetch_related(
        'hashtags', 'reactions', 'comments'
    ).order_by('-created_at')
    
    # Pagination
    paginator = Paginator(posts, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'hashtag': hashtag,
        'posts': page_obj,
        'posts_count': posts.count(),
    }
    
    return render(request, 'hashtags/detail.html', context)


@login_required
def bookmarks(request):
    """
    Bookmarks view.
    
    Shows user's bookmarked posts.
    """
    bookmarks = Bookmark.objects.filter(
        user=request.user
    ).select_related('post', 'post__user', 'post__user__profile').prefetch_related(
        'post__hashtags', 'post__reactions', 'post__comments'
    ).order_by('-created_at')
    
    # Pagination
    paginator = Paginator(bookmarks, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'bookmarks': page_obj,
    }
    
    return render(request, 'bookmarks.html', context)


@login_required
def notifications(request):
    """
    Notifications view.
    
    Shows user's notifications.
    """
    notifications = Notification.objects.filter(
        recipient=request.user
    ).select_related('sender', 'post', 'comment').order_by('-created_at')
    
    # Mark as read
    unread_notifications = notifications.filter(is_read=False)
    unread_notifications.update(is_read=True)
    
    # Pagination
    paginator = Paginator(notifications, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'notifications': page_obj,
    }
    
    return render(request, 'notifications.html', context)


@ratelimit(key='ip', rate='30/m', method='GET', block=True)
def search(request):
    """
    Search view.

    Allows users to search for posts, users, and hashtags.
    """
    query = request.GET.get('q', '').strip()
    search_type = request.GET.get('type', 'all')

    # Sanitize and validate query
    if not query or len(query) < 2:
        return render(request, 'search.html', {'query': '', 'results': []})

    if len(query) > 100:
        messages.error(request, "Search query too long.")
        return render(request, 'search.html', {'query': '', 'results': []})

    # Escape query to prevent XSS
    query = escape(query)

    results = []
    
    if search_type in ['all', 'posts']:
        posts = Post.objects.filter(
            Q(content__icontains=query) | Q(hashtags__name__icontains=query),
            is_public=True,
            is_deleted=False
        ).select_related('user', 'user__profile').prefetch_related('hashtags').distinct()
        results.extend(posts)
    
    if search_type in ['all', 'users']:
        users = User.objects.filter(
            Q(username__icontains=query) | Q(first_name__icontains=query) | Q(last_name__icontains=query)
        ).select_related('profile')
        results.extend(users)
    
    if search_type in ['all', 'hashtags']:
        hashtags = Hashtag.objects.filter(name__icontains=query)
        results.extend(hashtags)
    
    # Sort results by relevance (you can implement more sophisticated ranking)
    if search_type == 'all':
        results.sort(key=lambda x: getattr(x, 'created_at', timezone.now()), reverse=True)
    
    # Pagination
    paginator = Paginator(results, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'query': query,
        'search_type': search_type,
        'results': page_obj,
    }
    
    return render(request, 'search.html', context)