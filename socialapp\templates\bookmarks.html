{% extends 'base.html' %}

{% block title %}Bookmarks - BlogApp{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-bookmark"></i> Bookmarks</h2>
    {% if bookmarks %}
    <div class="d-flex gap-2">
        <button class="btn btn-outline-primary" onclick="exportBookmarks()">
            <i class="fas fa-download"></i> Export
        </button>
        <button class="btn btn-outline-danger" onclick="clearAllBookmarks()">
            <i class="fas fa-trash"></i> Clear All
        </button>
    </div>
    {% endif %}
</div>

{% if bookmarks %}
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-list"></i> Saved Posts ({{ bookmarks.paginator.count }})
            </h5>
            <div class="d-flex gap-2">
                <select class="form-select form-select-sm" id="sortBookmarks" style="width: auto;">
                    <option value="newest">Newest First</option>
                    <option value="oldest">Oldest First</option>
                    <option value="most_liked">Most Liked</option>
                    <option value="most_commented">Most Commented</option>
                </select>
            </div>
        </div>
    </div>
    
    <div class="card-body p-0">
        {% for bookmark in bookmarks %}
        <div class="border-bottom p-3 bookmark-item" data-post-id="{{ bookmark.post.pk }}">
            <div class="d-flex align-items-start">
                <img src="{{ bookmark.post.user.profile.get_avatar_url }}" 
                     alt="{{ bookmark.post.user.username }}" class="avatar-sm me-3">
                <div class="flex-grow-1">
                    <div class="d-flex align-items-center mb-1">
                        <h6 class="mb-0 fw-bold">{{ bookmark.post.user.username }}</h6>
                        {% if bookmark.post.user.profile.is_verified %}
                        <i class="fas fa-check-circle text-primary ms-1" title="Verified"></i>
                        {% endif %}
                        <small class="text-muted ms-2">
                            {{ bookmark.post.created_at|timesince }} ago
                            {% if not bookmark.post.is_public %}
                            <i class="fas fa-lock ms-1" title="Private post"></i>
                            {% endif %}
                        </small>
                        <small class="text-muted ms-auto">
                            <i class="fas fa-bookmark text-success"></i> Saved {{ bookmark.created_at|timesince }} ago
                        </small>
                    </div>
                    
                    <p class="mb-2">{{ bookmark.post.content|linebreaks }}</p>
                    
                    {% if bookmark.post.hashtags.all %}
                    <div class="mb-2">
                        {% for hashtag in bookmark.post.hashtags.all %}
                        <a href="{% url 'hashtag_detail' hashtag.slug %}" class="hashtag me-1">
                            #{{ hashtag.name }}
                        </a>
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    {% if bookmark.post.image %}
                    <div class="mb-2">
                        <img src="{{ bookmark.post.image.url }}" alt="Post image" 
                             class="img-fluid rounded" style="max-height: 300px; object-fit: cover;">
                    </div>
                    {% endif %}
                    
                    {% if bookmark.post.video %}
                    <div class="mb-2">
                        <video controls class="w-100 rounded" style="max-height: 300px;">
                            <source src="{{ bookmark.post.video.url }}" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                    </div>
                    {% endif %}
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="text-muted small">
                            <span><i class="fas fa-heart"></i> {{ bookmark.post.likes_count }} likes</span>
                            <span class="mx-2">•</span>
                            <span><i class="fas fa-comment"></i> {{ bookmark.post.comments_count }} comments</span>
                            <span class="mx-2">•</span>
                            <span><i class="fas fa-share"></i> {{ bookmark.post.shares_count }} shares</span>
                            <span class="mx-2">•</span>
                            <span><i class="fas fa-eye"></i> {{ bookmark.post.views_count }} views</span>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="{% url 'post_detail' bookmark.post.pk %}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i> View
                            </a>
                            <button class="btn btn-sm btn-outline-danger" onclick="removeBookmark('{{ bookmark.post.pk }}')">
                                <i class="fas fa-bookmark"></i> Remove
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Pagination -->
{% if bookmarks.has_other_pages %}
<nav aria-label="Bookmarks pagination" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if bookmarks.has_previous %}
        <li class="page-item">
            <a class="page-link" href="?page={{ bookmarks.previous_page_number }}">
                <i class="fas fa-chevron-left"></i> Previous
            </a>
        </li>
        {% endif %}

        {% for num in bookmarks.paginator.page_range %}
            {% if bookmarks.number == num %}
            <li class="page-item active">
                <span class="page-link">{{ num }}</span>
            </li>
            {% elif num > bookmarks.number|add:'-3' and num < bookmarks.number|add:'3' %}
            <li class="page-item">
                <a class="page-link" href="?page={{ num }}">{{ num }}</a>
            </li>
            {% endif %}
        {% endfor %}

        {% if bookmarks.has_next %}
        <li class="page-item">
            <a class="page-link" href="?page={{ bookmarks.next_page_number }}">
                Next <i class="fas fa-chevron-right"></i>
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<!-- No Bookmarks -->
<div class="card">
    <div class="card-body text-center py-5">
        <i class="fas fa-bookmark fa-3x text-muted mb-3"></i>
        <h5>No bookmarks yet</h5>
        <p class="text-muted">
            Posts you bookmark will appear here for easy access later.
        </p>
        <a href="{% url 'home' %}" class="btn btn-primary">
            <i class="fas fa-home"></i> Explore Posts
        </a>
    </div>
</div>
{% endif %}

<!-- Bookmark Statistics -->
{% if bookmarks %}
<div class="card mt-4">
    <div class="card-header">
        <h6 class="mb-0"><i class="fas fa-chart-bar"></i> Bookmark Statistics</h6>
    </div>
    <div class="card-body">
        <div class="row text-center">
            <div class="col-md-3">
                <h5 class="text-primary">{{ total_bookmarks }}</h5>
                <small class="text-muted">Total Bookmarks</small>
            </div>
            <div class="col-md-3">
                <h5 class="text-success">{{ this_week_bookmarks }}</h5>
                <small class="text-muted">This Week</small>
            </div>
            <div class="col-md-3">
                <h5 class="text-info">{{ this_month_bookmarks }}</h5>
                <small class="text-muted">This Month</small>
            </div>
            <div class="col-md-3">
                <h5 class="text-warning">{{ most_bookmarked_category }}</h5>
                <small class="text-muted">Most Saved Category</small>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Sort bookmarks
    var sortSelect = document.getElementById('sortBookmarks');
    if (sortSelect) {
        sortSelect.addEventListener('change', function() {
            var sortBy = this.value;
            // Here you would typically send an AJAX request to sort bookmarks
            console.log('Sort by:', sortBy);
        });
    }
});

function removeBookmark(postId) {
    if (confirm('Are you sure you want to remove this bookmark?')) {
        fetch('/bookmarks/' + postId + '/remove/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            },
        })
        .then(function(response) {
            return response.json();
        })
        .then(function(data) {
            if (data.success) {
                var bookmarkItem = document.querySelector('[data-post-id="' + postId + '"]');
                if (bookmarkItem) {
                    bookmarkItem.remove();
                }
                // Update bookmark count
                var bookmarkCount = document.querySelectorAll('.bookmark-item').length;
                if (bookmarkCount === 0) {
                    location.reload(); // Reload to show empty state
                }
            }
        })
        .catch(function(error) {
            console.error('Error:', error);
            alert('Failed to remove bookmark');
        });
    }
}

function clearAllBookmarks() {
    if (confirm('Are you sure you want to clear all bookmarks? This action cannot be undone.')) {
        fetch('/bookmarks/clear-all/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            },
        })
        .then(function(response) {
            return response.json();
        })
        .then(function(data) {
            if (data.success) {
                location.reload();
            }
        })
        .catch(function(error) {
            console.error('Error:', error);
            alert('Failed to clear bookmarks');
        });
    }
}

function exportBookmarks() {
    // This would typically export bookmarks to a file
    alert('Export feature coming soon!');
}
</script>
{% endblock %} 