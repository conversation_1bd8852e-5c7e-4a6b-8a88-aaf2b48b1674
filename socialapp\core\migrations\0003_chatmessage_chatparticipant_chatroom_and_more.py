# Generated by Django 5.2.3 on 2025-06-29 12:08

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0002_alter_post_image_alter_post_video_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ChatMessage",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        help_text="Unique identifier for the message",
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "message_type",
                    models.CharField(
                        choices=[
                            ("text", "Text"),
                            ("image", "Image"),
                            ("file", "File"),
                            ("system", "System"),
                        ],
                        default="text",
                        help_text="Type of message",
                        max_length=10,
                    ),
                ),
                (
                    "content",
                    models.TextField(
                        blank=True, help_text="Text content of the message"
                    ),
                ),
                (
                    "attachment",
                    models.FileField(
                        blank=True,
                        help_text="File attachment",
                        null=True,
                        upload_to="chat_attachments/%Y/%m/%d/",
                    ),
                ),
                (
                    "is_edited",
                    models.BooleanField(
                        default=False, help_text="Whether this message has been edited"
                    ),
                ),
                (
                    "edited_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When the message was last edited",
                        null=True,
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(
                        default=False, help_text="Whether this message has been deleted"
                    ),
                ),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, help_text="When the message was deleted", null=True
                    ),
                ),
                (
                    "reply_to",
                    models.ForeignKey(
                        blank=True,
                        help_text="Message this is replying to",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="replies",
                        to="core.chatmessage",
                    ),
                ),
                (
                    "sender",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who sent the message (null for system messages)",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sent_messages",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Chat Message",
                "verbose_name_plural": "Chat Messages",
                "ordering": ["created_at"],
            },
        ),
        migrations.CreateModel(
            name="ChatParticipant",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("admin", "Admin"),
                            ("moderator", "Moderator"),
                            ("member", "Member"),
                        ],
                        default="member",
                        help_text="Role of the participant",
                        max_length=10,
                    ),
                ),
                (
                    "joined_at",
                    models.DateTimeField(
                        auto_now_add=True, help_text="When the user joined the chat"
                    ),
                ),
                (
                    "last_read_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="Last time the user read messages",
                        null=True,
                    ),
                ),
                (
                    "is_muted",
                    models.BooleanField(
                        default=False,
                        help_text="Whether notifications are muted for this user",
                    ),
                ),
                (
                    "is_pinned",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this chat is pinned for the user",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        help_text="Participating user",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="chat_participations",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Chat Participant",
                "verbose_name_plural": "Chat Participants",
                "ordering": ["-joined_at"],
            },
        ),
        migrations.CreateModel(
            name="ChatRoom",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        help_text="Unique identifier for the chat room",
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True,
                        help_text="Name of the chat room (for group chats)",
                        max_length=100,
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Description of the chat room",
                        max_length=500,
                    ),
                ),
                (
                    "room_type",
                    models.CharField(
                        choices=[
                            ("private", "Private Chat"),
                            ("group", "Group Chat"),
                            ("public", "Public Chat"),
                        ],
                        default="private",
                        help_text="Type of chat room",
                        max_length=10,
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, help_text="Whether the chat room is active"
                    ),
                ),
                (
                    "last_activity",
                    models.DateTimeField(
                        auto_now=True, help_text="Last activity in this chat room"
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        help_text="User who created this chat room",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_chat_rooms",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "last_message",
                    models.ForeignKey(
                        blank=True,
                        help_text="Last message in this chat room",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to="core.chatmessage",
                    ),
                ),
                (
                    "participants",
                    models.ManyToManyField(
                        help_text="Users participating in this chat",
                        related_name="chat_rooms",
                        through="core.ChatParticipant",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Chat Room",
                "verbose_name_plural": "Chat Rooms",
                "ordering": ["-last_activity"],
            },
        ),
        migrations.AddField(
            model_name="chatparticipant",
            name="chat_room",
            field=models.ForeignKey(
                help_text="Chat room",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="participants_details",
                to="core.chatroom",
            ),
        ),
        migrations.AddField(
            model_name="chatmessage",
            name="chat_room",
            field=models.ForeignKey(
                help_text="Chat room this message belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="messages",
                to="core.chatroom",
            ),
        ),
        migrations.CreateModel(
            name="UserOnlineStatus",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("online", "Online"),
                            ("away", "Away"),
                            ("busy", "Busy"),
                            ("offline", "Offline"),
                        ],
                        default="offline",
                        help_text="Current online status",
                        max_length=10,
                    ),
                ),
                (
                    "last_seen",
                    models.DateTimeField(
                        auto_now=True, help_text="Last time the user was seen online"
                    ),
                ),
                (
                    "is_typing_in",
                    models.ForeignKey(
                        blank=True,
                        help_text="Chat room the user is currently typing in",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="typing_users",
                        to="core.chatroom",
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        help_text="User whose status is being tracked",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="online_status",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Online Status",
                "verbose_name_plural": "User Online Statuses",
            },
        ),
        migrations.AddIndex(
            model_name="chatroom",
            index=models.Index(
                fields=["room_type", "-last_activity"],
                name="core_chatro_room_ty_2353c7_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="chatroom",
            index=models.Index(
                fields=["is_active", "-last_activity"],
                name="core_chatro_is_acti_9867f3_idx",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="chatparticipant",
            unique_together={("chat_room", "user")},
        ),
        migrations.AddIndex(
            model_name="chatmessage",
            index=models.Index(
                fields=["chat_room", "created_at"],
                name="core_chatme_chat_ro_dfc5b7_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="chatmessage",
            index=models.Index(
                fields=["sender", "created_at"], name="core_chatme_sender__f54148_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="chatmessage",
            index=models.Index(
                fields=["is_deleted", "created_at"],
                name="core_chatme_is_dele_fb8f7d_idx",
            ),
        ),
    ]
