<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}BlogApp - Share Your Thoughts{% endblock %}</title>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Animate.css for smooth animations -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <!-- Custom CSS -->
    <style>
        :root {
            /* Modern Color Palette */
            --primary-color: #3b82f6;
            --primary-hover: #2563eb;
            --primary-light: #dbeafe;
            --secondary-color: #6b7280;
            --accent-color: #10b981;
            --accent-hover: #059669;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --success-color: #10b981;

            /* Background Colors */
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --bg-dark: #1e293b;

            /* Text Colors */
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --text-light: #ffffff;

            /* Border & Shadow */
            --border-color: #e2e8f0;
            --border-light: #f1f5f9;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

            /* Spacing */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;

            /* Transitions */
            --transition-fast: 0.15s ease-in-out;
            --transition-normal: 0.3s ease-in-out;
            --transition-slow: 0.5s ease-in-out;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
            color: var(--text-primary);
            line-height: 1.6;
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        .navbar {
            background: var(--bg-primary);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            box-shadow: var(--shadow-sm);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .navbar-brand {
            font-weight: 700;
            color: var(--primary-color) !important;
            font-size: 1.75rem;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition-fast);
        }

        .navbar-brand:hover {
            color: var(--primary-hover) !important;
            transform: translateY(-1px);
        }

        .navbar-brand i {
            font-size: 1.5rem;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-link {
            color: var(--text-secondary) !important;
            font-weight: 500;
            padding: 0.75rem 1rem !important;
            border-radius: var(--radius-md);
            transition: var(--transition-fast);
            position: relative;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .nav-link:hover {
            background: var(--primary-light);
            color: var(--primary-color) !important;
            transform: translateY(-1px);
        }

        .nav-link.active {
            background: var(--primary-color);
            color: var(--text-light) !important;
        }

        .nav-link i {
            font-size: 1rem;
            width: 20px;
            text-align: center;
        }

        /* Buttons */
        .btn {
            border-radius: var(--radius-md);
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            transition: var(--transition-fast);
            border: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            color: var(--text-light);
            box-shadow: var(--shadow-sm);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
            color: var(--text-light);
        }

        .btn-secondary {
            background: var(--bg-secondary);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--bg-tertiary);
            color: var(--text-primary);
            transform: translateY(-1px);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color), var(--accent-hover));
            color: var(--text-light);
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color), #dc2626);
            color: var(--text-light);
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }

        .btn-lg {
            padding: 1rem 2rem;
            font-size: 1.125rem;
        }

        /* Cards */
        .card {
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            transition: var(--transition-normal);
            background: var(--bg-primary);
            overflow: hidden;
        }

        .card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .card-header {
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-color);
            padding: 1.25rem;
            font-weight: 600;
        }

        .card-body {
            padding: 1.5rem;
        }

        .card-footer {
            background: var(--bg-secondary);
            border-top: 1px solid var(--border-color);
            padding: 1rem 1.5rem;
        }

        /* Forms */
        .form-control {
            border: 2px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: 0.75rem 1rem;
            transition: var(--transition-fast);
            background: var(--bg-primary);
            font-size: 1rem;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--primary-light);
            outline: none;
        }

        .form-label {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        /* Search */
        .search-box {
            border-radius: var(--radius-xl);
            padding: 0.75rem 1.25rem;
            border: 2px solid var(--border-color);
            background: var(--bg-secondary);
            transition: var(--transition-fast);
            width: 300px;
        }

        .search-box:focus {
            border-color: var(--primary-color);
            background: var(--bg-primary);
            box-shadow: var(--shadow-sm);
            outline: none;
        }

        .card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .post-card {
            margin-bottom: 1rem;
            border-radius: 12px;
            overflow: hidden;
        }

        .post-header {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .post-content {
            padding: 1rem;
        }

        /* Post Actions */
        .post-actions {
            padding: 1rem 1.5rem;
            border-top: 1px solid var(--border-color);
            background: var(--bg-secondary);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .action-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            padding: 0.75rem;
            border-radius: var(--radius-md);
            transition: var(--transition-fast);
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
            text-decoration: none;
            margin-right: 0.5rem;
        }

        .action-btn:hover {
            background: var(--primary-light);
            color: var(--primary-color);
            transform: translateY(-1px);
        }

        .action-btn.liked {
            color: var(--danger-color);
            background: rgba(239, 68, 68, 0.1);
        }

        .action-btn.liked:hover {
            background: rgba(239, 68, 68, 0.2);
            color: var(--danger-color);
        }

        .reaction-btn {
            position: relative;
        }

        .reaction-btn.liked i {
            animation: heartBeat 0.6s ease-in-out;
        }

        @keyframes heartBeat {
            0% { transform: scale(1); }
            25% { transform: scale(1.2); }
            50% { transform: scale(1); }
            75% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .action-btn.bookmarked {
            color: #10b981;
        }

        .avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            object-fit: cover;
        }

        .avatar-sm {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            object-fit: cover;
        }

        .hashtag {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }

        .hashtag:hover {
            text-decoration: underline;
        }

        .sidebar {
            position: sticky;
            top: 1rem;
        }

        .trending-item {
            padding: 0.75rem;
            border-radius: 8px;
            transition: background-color 0.2s;
        }

        .trending-item:hover {
            background-color: var(--hover-color);
        }

        .notification-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: linear-gradient(135deg, var(--danger-color), #dc2626);
            color: var(--text-light);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.75rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-sm);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .search-box {
            border-radius: 20px;
            border: 1px solid var(--border-color);
            padding: 0.5rem 1rem;
        }

        .search-box:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
        }

        .footer {
            background-color: var(--hover-color);
            border-top: 1px solid var(--border-color);
            padding: 2rem 0;
            margin-top: 3rem;
        }

        @media (max-width: 768px) {
            .sidebar {
                display: none;
            }
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand animate__animated animate__fadeInLeft" href="{% url 'home' %}">
                <i class="fas fa-feather"></i>
                <span class="text-gradient">SocialHub</span>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'home' %}active{% endif %}" href="{% url 'home' %}" title="Home Feed">
                            <i class="fas fa-home"></i>
                            <span>Home</span>
                        </a>
                    </li>
                    {% if user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'explore' %}active{% endif %}" href="{% url 'search' %}" title="Explore Posts">
                            <i class="fas fa-compass"></i>
                            <span>Explore</span>
                        </a>
                    </li>
                    <li class="nav-item position-relative">
                        <a class="nav-link {% if request.resolver_match.url_name == 'notifications' %}active{% endif %}" href="{% url 'notifications' %}" title="Notifications">
                            <i class="fas fa-bell"></i>
                            <span>Notifications</span>
                            {% if unread_notifications_count %}
                            <span class="notification-badge">{{ unread_notifications_count }}</span>
                            {% endif %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'bookmarks' %}active{% endif %}" href="{% url 'bookmarks' %}" title="Saved Posts">
                            <i class="fas fa-bookmark"></i>
                            <span>Bookmarks</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'chat_home' %}active{% endif %}" href="{% url 'chat_home' %}" title="Messages">
                            <i class="fas fa-comments"></i>
                            <span>Messages</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
                
                <!-- Search Bar -->
                <form class="d-flex me-3" method="GET" action="{% url 'search' %}" role="search">
                    <div class="position-relative">
                        <input class="form-control search-box" type="search" name="q"
                               placeholder="Search posts, users..."
                               value="{{ request.GET.q }}" aria-label="Search"
                               autocomplete="off">
                        <i class="fas fa-search position-absolute" style="right: 15px; top: 50%; transform: translateY(-50%); color: var(--text-muted);"></i>
                    </div>
                </form>
                
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                    <!-- Quick Actions -->
                    <li class="nav-item me-2">
                        <a class="btn btn-primary btn-sm" href="{% url 'create_post' %}" title="Create New Post">
                            <i class="fas fa-plus"></i>
                            <span class="d-none d-md-inline">Post</span>
                        </a>
                    </li>

                    <!-- User Menu -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="navbarDropdown"
                           role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <img src="{{ user.profile.get_avatar_url }}" alt="{{ user.username }}"
                                 class="avatar-sm rounded-circle me-2">
                            <span class="d-none d-md-inline">{{ user.get_full_name|default:user.username }}</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="navbarDropdown">
                            <li class="dropdown-header">
                                <div class="d-flex align-items-center">
                                    <img src="{{ user.profile.get_avatar_url }}" alt="{{ user.username }}"
                                         class="avatar-sm rounded-circle me-2">
                                    <div>
                                        <div class="fw-bold">{{ user.get_full_name|default:user.username }}</div>
                                        <small class="text-muted">@{{ user.username }}</small>
                                    </div>
                                </div>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'profile' user.username %}">
                                <i class="fas fa-user me-2"></i> My Profile
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'edit_profile' %}">
                                <i class="fas fa-edit me-2"></i> Edit Profile
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'bookmarks' %}">
                                <i class="fas fa-bookmark me-2"></i> Saved Posts
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="{% url 'logout' %}">
                                <i class="fas fa-sign-out-alt me-2"></i> Logout
                            </a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item me-2">
                        <a class="btn btn-outline-primary btn-sm" href="{% url 'login' %}">
                            <i class="fas fa-sign-in-alt"></i> Login
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="btn btn-primary btn-sm" href="{% url 'register' %}">
                            <i class="fas fa-user-plus"></i> Sign Up
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Messages -->
    {% if messages %}
    <div class="container mt-3">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Main Content -->
    <main class="container mt-4">
        <div class="row">
            <!-- Main Content Area -->
            <div class="col-lg-8">
                {% block content %}{% endblock %}
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="sidebar">
                    {% if user.is_authenticated %}
                    <!-- Create Post Button -->
                    <div class="card mb-3">
                        <div class="card-body text-center">
                            <a href="{% url 'create_post' %}" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-plus"></i> Create Post
                            </a>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- Trending Hashtags -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-fire"></i> Trending Hashtags</h6>
                        </div>
                        <div class="card-body p-0">
                            {% for hashtag in trending_hashtags %}
                            <a href="{% url 'hashtag_detail' hashtag.slug %}" class="d-block trending-item">
                                <div class="fw-bold">#{{ hashtag.name }}</div>
                                <small class="text-muted">{{ hashtag.posts_count }} posts</small>
                            </a>
                            {% empty %}
                            <div class="p-3 text-muted">No trending hashtags</div>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <!-- Suggested Users -->
                    {% if user.is_authenticated %}
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-users"></i> Suggested Users</h6>
                        </div>
                        <div class="card-body p-0">
                            {% for suggested_user in suggested_users %}
                            <div class="d-flex align-items-center p-3 border-bottom">
                                <img src="{{ suggested_user.profile.get_avatar_url }}" 
                                     alt="{{ suggested_user.username }}" class="avatar-sm me-3">
                                <div class="flex-grow-1">
                                    <div class="fw-bold">{{ suggested_user.username }}</div>
                                    <small class="text-muted">{{ suggested_user.profile.bio|truncatechars:50 }}</small>
                                </div>
                                <a href="{% url 'follow_user' suggested_user.username %}" 
                                   class="btn btn-sm btn-outline-primary">Follow</a>
                            </div>
                            {% empty %}
                            <div class="p-3 text-muted">No suggestions available</div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>BlogApp</h5>
                    <p class="text-muted">Share your thoughts with the world.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted">&copy; 2025 BlogApp. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // Get CSRF token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
        const csrftoken = getCookie('csrftoken');

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            var alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                var bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);

        // Handle reaction buttons with AJAX
        document.addEventListener('DOMContentLoaded', function() {
            const reactionButtons = document.querySelectorAll('.reaction-btn');
            reactionButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const form = this.closest('form');
                    if (form) {
                        const formData = new FormData(form);

                        fetch(form.action, {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest',
                                'X-CSRFToken': csrftoken
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Update button state
                                if (data.reacted) {
                                    this.classList.add('liked');
                                } else {
                                    this.classList.remove('liked');
                                }

                                // Update likes count
                                const likesSpan = document.querySelector(`[data-post-id="${form.action.split('/')[2]}"] .likes-count`);
                                if (likesSpan) {
                                    likesSpan.textContent = `${data.likes_count} likes`;
                                }
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            // Fallback to form submission
                            form.submit();
                        });
                    }
                });
            });
        });
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html>
