<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}BlogApp - Share Your Thoughts{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #1f2937;
            --background-color: #ffffff;
            --text-color: #1f2937;
            --border-color: #e5e7eb;
            --hover-color: #f9fafb;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
        }

        .navbar {
            background-color: var(--background-color);
            border-bottom: 1px solid var(--border-color);
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: bold;
            color: var(--primary-color) !important;
            font-size: 1.5rem;
        }

        .nav-link {
            color: var(--text-color) !important;
            font-weight: 500;
            padding: 0.5rem 1rem !important;
            border-radius: 20px;
            transition: background-color 0.2s;
        }

        .nav-link:hover {
            background-color: var(--hover-color);
            color: var(--primary-color) !important;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            border-radius: 20px;
            font-weight: 600;
            padding: 0.5rem 1.5rem;
        }

        .btn-primary:hover {
            background-color: #5855eb;
            border-color: #5855eb;
        }

        .card {
            border: 1px solid var(--border-color);
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .post-card {
            margin-bottom: 1rem;
            border-radius: 12px;
            overflow: hidden;
        }

        .post-header {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .post-content {
            padding: 1rem;
        }

        .post-actions {
            padding: 0.5rem 1rem;
            border-top: 1px solid var(--border-color);
            background-color: var(--hover-color);
        }

        .action-btn {
            background: none;
            border: none;
            color: #6b7280;
            padding: 0.5rem;
            border-radius: 50%;
            transition: all 0.2s;
            margin-right: 0.5rem;
        }

        .action-btn:hover {
            background-color: rgba(99, 102, 241, 0.1);
            color: var(--primary-color);
        }

        .action-btn.liked {
            color: #ef4444;
        }

        .action-btn.bookmarked {
            color: #10b981;
        }

        .avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            object-fit: cover;
        }

        .avatar-sm {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            object-fit: cover;
        }

        .hashtag {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }

        .hashtag:hover {
            text-decoration: underline;
        }

        .sidebar {
            position: sticky;
            top: 1rem;
        }

        .trending-item {
            padding: 0.75rem;
            border-radius: 8px;
            transition: background-color 0.2s;
        }

        .trending-item:hover {
            background-color: var(--hover-color);
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #ef4444;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .search-box {
            border-radius: 20px;
            border: 1px solid var(--border-color);
            padding: 0.5rem 1rem;
        }

        .search-box:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
        }

        .footer {
            background-color: var(--hover-color);
            border-top: 1px solid var(--border-color);
            padding: 2rem 0;
            margin-top: 3rem;
        }

        @media (max-width: 768px) {
            .sidebar {
                display: none;
            }
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="{% url 'home' %}">
                <i class="fas fa-feather"></i> BlogApp
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'home' %}">
                            <i class="fas fa-home"></i> Home
                        </a>
                    </li>
                    {% if user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'notifications' %}">
                            <i class="fas fa-bell"></i> Notifications
                            {% if unread_notifications_count %}
                            <span class="notification-badge">{{ unread_notifications_count }}</span>
                            {% endif %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'bookmarks' %}">
                            <i class="fas fa-bookmark"></i> Bookmarks
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'chat_home' %}">
                            <i class="fas fa-comments"></i> Chat
                        </a>
                    </li>
                    {% endif %}
                </ul>
                
                <!-- Search Bar -->
                <form class="d-flex me-3" method="GET" action="{% url 'search' %}">
                    <input class="form-control search-box" type="search" name="q" placeholder="Search..." 
                           value="{{ request.GET.q }}" aria-label="Search">
                </form>
                
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" 
                           data-bs-toggle="dropdown" aria-expanded="false">
                            <img src="{{ user.profile.get_avatar_url }}" alt="{{ user.username }}" class="avatar-sm">
                            {{ user.username }}
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="navbarDropdown">
                            <li><a class="dropdown-item" href="{% url 'profile' user.username %}">
                                <i class="fas fa-user"></i> Profile
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'edit_profile' %}">
                                <i class="fas fa-user-edit"></i> Edit Profile
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'logout' %}">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'login' %}">Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="btn btn-primary" href="{% url 'register' %}">Sign Up</a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Messages -->
    {% if messages %}
    <div class="container mt-3">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Main Content -->
    <main class="container mt-4">
        <div class="row">
            <!-- Main Content Area -->
            <div class="col-lg-8">
                {% block content %}{% endblock %}
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="sidebar">
                    {% if user.is_authenticated %}
                    <!-- Create Post Button -->
                    <div class="card mb-3">
                        <div class="card-body text-center">
                            <a href="{% url 'create_post' %}" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-plus"></i> Create Post
                            </a>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- Trending Hashtags -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-fire"></i> Trending Hashtags</h6>
                        </div>
                        <div class="card-body p-0">
                            {% for hashtag in trending_hashtags %}
                            <a href="{% url 'hashtag_detail' hashtag.slug %}" class="d-block trending-item">
                                <div class="fw-bold">#{{ hashtag.name }}</div>
                                <small class="text-muted">{{ hashtag.posts_count }} posts</small>
                            </a>
                            {% empty %}
                            <div class="p-3 text-muted">No trending hashtags</div>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <!-- Suggested Users -->
                    {% if user.is_authenticated %}
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-users"></i> Suggested Users</h6>
                        </div>
                        <div class="card-body p-0">
                            {% for suggested_user in suggested_users %}
                            <div class="d-flex align-items-center p-3 border-bottom">
                                <img src="{{ suggested_user.profile.get_avatar_url }}" 
                                     alt="{{ suggested_user.username }}" class="avatar-sm me-3">
                                <div class="flex-grow-1">
                                    <div class="fw-bold">{{ suggested_user.username }}</div>
                                    <small class="text-muted">{{ suggested_user.profile.bio|truncatechars:50 }}</small>
                                </div>
                                <a href="{% url 'follow_user' suggested_user.username %}" 
                                   class="btn btn-sm btn-outline-primary">Follow</a>
                            </div>
                            {% empty %}
                            <div class="p-3 text-muted">No suggestions available</div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>BlogApp</h5>
                    <p class="text-muted">Share your thoughts with the world.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted">&copy; 2025 BlogApp. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // Get CSRF token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
        const csrftoken = getCookie('csrftoken');

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            var alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                var bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);

        // Handle reaction buttons with AJAX
        document.addEventListener('DOMContentLoaded', function() {
            const reactionButtons = document.querySelectorAll('.reaction-btn');
            reactionButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const form = this.closest('form');
                    if (form) {
                        const formData = new FormData(form);

                        fetch(form.action, {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest',
                                'X-CSRFToken': csrftoken
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Update button state
                                if (data.reacted) {
                                    this.classList.add('liked');
                                } else {
                                    this.classList.remove('liked');
                                }

                                // Update likes count
                                const likesSpan = document.querySelector(`[data-post-id="${form.action.split('/')[2]}"] .likes-count`);
                                if (likesSpan) {
                                    likesSpan.textContent = `${data.likes_count} likes`;
                                }
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            // Fallback to form submission
                            form.submit();
                        });
                    }
                });
            });
        });
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html>
