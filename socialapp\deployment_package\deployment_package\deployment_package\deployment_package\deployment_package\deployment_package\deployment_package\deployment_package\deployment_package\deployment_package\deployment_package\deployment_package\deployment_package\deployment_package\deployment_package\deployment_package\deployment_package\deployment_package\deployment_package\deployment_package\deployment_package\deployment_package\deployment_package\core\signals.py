# type: ignore
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.contrib.auth.models import User
from django.utils import timezone
from .models import Profile, Post, Follow, Hashtag

@receiver(post_save, sender=User)
def create_profile(sender, instance, created, **kwargs):
    """Create a profile when a new user is created."""
    if created:
        Profile.objects.create(user=instance)


@receiver(post_save, sender=User)
def save_profile(sender, instance, **kwargs):
    """Save the profile when the user is saved."""
    if hasattr(instance, 'profile'):
        instance.profile.save()


@receiver(post_save, sender=Post)
def update_user_posts_count(sender, instance, created, **kwargs):
    """Update user's posts count when a post is created."""
    if created:
        instance.user.profile.update_counts()


@receiver(post_delete, sender=Post)
def update_user_posts_count_on_delete(sender, instance, **kwargs):
    """Update user's posts count when a post is deleted."""
    instance.user.profile.update_counts()


@receiver(post_save, sender=Follow)
def update_follow_counts(sender, instance, created, **kwargs):
    """Update follower/following counts when a follow relationship is created."""
    if created:
        instance.follower.profile.update_counts()
        instance.following.profile.update_counts()


@receiver(post_delete, sender=Follow)
def update_follow_counts_on_delete(sender, instance, **kwargs):
    """Update follower/following counts when a follow relationship is deleted."""
    instance.follower.profile.update_counts()
    instance.following.profile.update_counts()
