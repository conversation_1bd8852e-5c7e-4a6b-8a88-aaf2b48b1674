{% extends 'base.html' %}

{% block title %}{{ profile_user.username }}'s Profile - BlogApp{% endblock %}

{% block content %}
<!-- Profile Header -->
<div class="card mb-4">
    <div class="position-relative">
        <!-- Cover Photo -->
        <div style="height: 200px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            {% if profile_user.profile.cover_photo %}
            <img src="{{ profile_user.profile.cover_photo.url }}" 
                 alt="Cover photo" class="w-100 h-100" style="object-fit: cover;">
            {% endif %}
        </div>
        
        <!-- Profile Picture -->
        <div class="position-absolute" style="bottom: -50px; left: 30px;">
            <img src="{{ profile_user.profile.get_avatar_url }}" 
                 alt="{{ profile_user.username }}" 
                 class="rounded-circle border-4 border-white" 
                 style="width: 100px; height: 100px; object-fit: cover;">
        </div>
        
        <!-- Follow Button -->
        {% if user.is_authenticated and user != profile_user %}
        <div class="position-absolute" style="bottom: 20px; right: 30px;">
            <a href="{% url 'follow_user' profile_user.username %}" 
               class="btn {% if is_following %}btn-outline-primary{% else %}btn-primary{% endif %}">
                {% if is_following %}
                <i class="fas fa-user-minus"></i> Unfollow
                {% else %}
                <i class="fas fa-user-plus"></i> Follow
                {% endif %}
            </a>
        </div>
        {% endif %}
    </div>
    
    <!-- Profile Info -->
    <div class="card-body pt-5">
        <div class="row">
            <div class="col-md-8">
                <div class="d-flex align-items-center mb-2">
                    <h4 class="mb-0">{{ profile_user.username }}</h4>
                    {% if profile_user.profile.is_verified %}
                    <i class="fas fa-check-circle text-primary ms-2" title="Verified"></i>
                    {% endif %}
                </div>
                
                {% if profile_user.profile.bio %}
                <p class="text-muted mb-3">{{ profile_user.profile.bio }}</p>
                {% endif %}
                
                <div class="d-flex flex-wrap gap-3 text-muted small">
                    {% if profile_user.profile.location %}
                    <span><i class="fas fa-map-marker-alt"></i> {{ profile_user.profile.location }}</span>
                    {% endif %}
                    {% if profile_user.profile.website %}
                    <span><i class="fas fa-globe"></i> <a href="{{ profile_user.profile.website }}" target="_blank">Website</a></span>
                    {% endif %}
                    <span><i class="fas fa-calendar"></i> Joined {{ profile_user.date_joined|date:"F Y" }}</span>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <h5 class="text-primary mb-0">{{ posts_count }}</h5>
                            <small class="text-muted">Posts</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h5 class="text-success mb-0">{{ followers_count }}</h5>
                            <small class="text-muted">Followers</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h5 class="text-info mb-0">{{ following_count }}</h5>
                        <small class="text-muted">Following</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Posts Section -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list"></i> Posts by {{ profile_user.username }}
        </h5>
    </div>
    
    <div class="card-body p-0">
        {% if posts %}
            {% for post in posts %}
            <div class="border-bottom p-3">
                <div class="d-flex align-items-start">
                    <img src="{{ post.user.profile.get_avatar_url }}" 
                         alt="{{ post.user.username }}" class="avatar-sm me-3">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-1">
                            <h6 class="mb-0 fw-bold">{{ post.user.username }}</h6>
                            {% if post.user.profile.is_verified %}
                            <i class="fas fa-check-circle text-primary ms-1" title="Verified"></i>
                            {% endif %}
                            <small class="text-muted ms-2">
                                {{ post.created_at|timesince }} ago
                                {% if not post.is_public %}
                                <i class="fas fa-lock ms-1" title="Private post"></i>
                                {% endif %}
                            </small>
                        </div>
                        
                        <p class="mb-2">{{ post.content|truncatechars:200 }}</p>
                        
                        {% if post.hashtags.all %}
                        <div class="mb-2">
                            {% for hashtag in post.hashtags.all %}
                            <a href="{% url 'hashtag_detail' hashtag.slug %}" class="hashtag me-1">
                                #{{ hashtag.name }}
                            </a>
                            {% endfor %}
                        </div>
                        {% endif %}
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-muted small">
                                <span><i class="fas fa-heart"></i> {{ post.likes_count }}</span>
                                <span class="mx-2">•</span>
                                <span><i class="fas fa-comment"></i> {{ post.comments_count }}</span>
                                <span class="mx-2">•</span>
                                <span><i class="fas fa-eye"></i> {{ post.views_count }}</span>
                            </div>
                            <a href="{% url 'post_detail' post.pk %}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i> View
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
            
            <!-- Pagination -->
            {% if posts.has_other_pages %}
            <div class="p-3">
                <nav aria-label="Profile posts pagination">
                    <ul class="pagination justify-content-center mb-0">
                        {% if posts.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ posts.previous_page_number }}">
                                <i class="fas fa-chevron-left"></i> Previous
                            </a>
                        </li>
                        {% endif %}

                        {% for num in posts.paginator.page_range %}
                            {% if posts.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                            {% elif num > posts.number|add:'-3' and num < posts.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                            </li>
                            {% endif %}
                        {% endfor %}

                        {% if posts.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ posts.next_page_number }}">
                                Next <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
            
        {% else %}
        <!-- No Posts -->
        <div class="p-5 text-center">
            <i class="fas fa-feather fa-3x text-muted mb-3"></i>
            <h6>No posts yet</h6>
            <p class="text-muted">
                {% if user == profile_user %}
                You haven't posted anything yet. 
                <a href="{% url 'create_post' %}" class="text-decoration-none">Create your first post!</a>
                {% else %}
                {{ profile_user.username }} hasn't posted anything yet.
                {% endif %}
            </p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
