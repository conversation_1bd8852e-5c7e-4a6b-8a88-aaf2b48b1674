services:
  - type: web
    name: socialapp
    env: python
    buildCommand: "pip install -r requirements-production.txt && python manage.py collectstatic --noinput && python manage.py migrate"
    startCommand: "gunicorn --config gunicorn.conf.py socialapp.wsgi:application"
    envVars:
      - key: DJANGO_ENVIRONMENT
        value: production
      - key: DJANGO_DEBUG
        value: False
      - key: DJANGO_SECRET_KEY
        generateValue: true
      - key: DJANGO_ALLOWED_HOSTS
        value: "*"
      - key: DATABASE_URL
        fromDatabase:
          name: socialapp-db
          property: connectionString
      - key: REDIS_URL
        fromService:
          type: redis
          name: socialapp-redis
          property: connectionString

  - type: postgres
    name: socialapp-db
    databaseName: socialapp
    user: socialapp

  - type: redis
    name: socialapp-redis
    maxmemoryPolicy: allkeys-lru
