{% extends 'base.html' %}

{% block title %}Create Post - BlogApp{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0"><i class="fas fa-edit"></i> Create New Post</h4>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    <!-- Post Content -->
                    <div class="mb-3">
                        <label for="content" class="form-label">What's on your mind?</label>
                        <textarea class="form-control" id="content" name="content" rows="6" 
                                  placeholder="Share your thoughts, ideas, or experiences..." 
                                  maxlength="10000" required></textarea>
                        <div class="form-text">
                            <span id="char-count">0</span>/10000 characters
                        </div>
                    </div>

                    <!-- Media Upload -->
                    <div class="mb-3">
                        <label class="form-label">Add Media (Optional)</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-2">
                                    <label for="image" class="form-label">
                                        <i class="fas fa-image"></i> Image
                                    </label>
                                    <input type="file" class="form-control" id="image" name="image" 
                                           accept="image/*">
                                    <div class="form-text">Max 10MB. Supported: JPEG, PNG, GIF</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-2">
                                    <label for="video" class="form-label">
                                        <i class="fas fa-video"></i> Video
                                    </label>
                                    <input type="file" class="form-control" id="video" name="video" 
                                           accept="video/*">
                                    <div class="form-text">Max 100MB. Supported: MP4, AVI, MOV</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Privacy Settings -->
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_public" name="is_public" checked>
                            <label class="form-check-label" for="is_public">
                                <i class="fas fa-globe"></i> Make this post public
                            </label>
                        </div>
                        <div class="form-text">
                            Public posts are visible to everyone. Uncheck to make it private (only you can see it).
                        </div>
                    </div>

                    <!-- Hashtag Suggestions -->
                    <div class="mb-3">
                        <label class="form-label">Popular Hashtags</label>
                        <div class="d-flex flex-wrap gap-2">
                            {% for hashtag in trending_hashtags|slice:":10" %}
                            <button type="button" class="btn btn-outline-primary btn-sm hashtag-suggestion" 
                                    data-hashtag="#{{ hashtag.name }}">
                                #{{ hashtag.name }}
                            </button>
                            {% endfor %}
                        </div>
                        <div class="form-text">
                            Click on hashtags to add them to your post, or type # followed by your own hashtag.
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'home' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i> Post
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Posting Tips -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-lightbulb"></i> Posting Tips</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li><i class="fas fa-check text-success me-2"></i>Use hashtags to reach more people</li>
                    <li><i class="fas fa-check text-success me-2"></i>Add images or videos to make your post engaging</li>
                    <li><i class="fas fa-check text-success me-2"></i>Keep your content authentic and meaningful</li>
                    <li><i class="fas fa-check text-success me-2"></i>Respect community guidelines</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const contentTextarea = document.getElementById('content');
    const charCount = document.getElementById('char-count');
    const hashtagSuggestions = document.querySelectorAll('.hashtag-suggestion');

    // Character counter
    contentTextarea.addEventListener('input', function() {
        const count = this.value.length;
        charCount.textContent = count;
        
        if (count > 9500) {
            charCount.classList.add('text-danger');
        } else {
            charCount.classList.remove('text-danger');
        }
    });

    // Hashtag suggestions
    hashtagSuggestions.forEach(button => {
        button.addEventListener('click', function() {
            const hashtag = this.dataset.hashtag;
            const currentValue = contentTextarea.value;
            const cursorPos = contentTextarea.selectionStart;
            
            // Insert hashtag at cursor position
            const newValue = currentValue.slice(0, cursorPos) + hashtag + ' ' + currentValue.slice(cursorPos);
            contentTextarea.value = newValue;
            
            // Update character count
            charCount.textContent = newValue.length;
            
            // Focus back to textarea
            contentTextarea.focus();
            contentTextarea.setSelectionRange(cursorPos + hashtag.length + 1, cursorPos + hashtag.length + 1);
        });
    });

    // File size validation
    const imageInput = document.getElementById('image');
    const videoInput = document.getElementById('video');

    imageInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file && file.size > 10 * 1024 * 1024) {
            alert('Image file size must be less than 10MB');
            this.value = '';
        }
    });

    videoInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file && file.size > 100 * 1024 * 1024) {
            alert('Video file size must be less than 100MB');
            this.value = '';
        }
    });

    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const content = contentTextarea.value.trim();
        if (!content) {
            e.preventDefault();
            alert('Please enter some content for your post');
            contentTextarea.focus();
        }
    });
});
</script>
{% endblock %} 