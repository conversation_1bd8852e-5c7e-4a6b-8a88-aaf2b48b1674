"""
WebSocket consumers for real-time chat functionality.
"""

import json
import uuid
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth.models import User
from django.utils import timezone
from .models import ChatRoom, ChatMessage, ChatParticipant, UserOnlineStatus


class ChatConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for real-time chat messages.
    """

    async def connect(self):
        """Handle WebSocket connection."""
        self.room_id = self.scope['url_route']['kwargs']['room_id']
        self.room_group_name = f'chat_{self.room_id}'
        self.user = self.scope['user']

        # Check if user is authenticated
        if not self.user.is_authenticated:
            await self.close()
            return

        # Check if user is participant in the chat room
        if not await self.is_participant():
            await self.close()
            return

        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )

        await self.accept()

        # Set user as online
        await self.set_user_online()

        # Send user join notification to room
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'user_join',
                'user': self.user.username,
                'user_id': self.user.id,
            }
        )

    async def disconnect(self, close_code):
        """Handle WebSocket disconnection."""
        if hasattr(self, 'room_group_name'):
            # Send user leave notification to room
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'user_leave',
                    'user': self.user.username,
                    'user_id': self.user.id,
                }
            )

            # Leave room group
            await self.channel_layer.group_discard(
                self.room_group_name,
                self.channel_name
            )

        # Set user as offline
        await self.set_user_offline()

    async def receive(self, text_data):
        """Handle received WebSocket message."""
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type', 'message')

            if message_type == 'message':
                await self.handle_chat_message(text_data_json)
            elif message_type == 'typing':
                await self.handle_typing(text_data_json)
            elif message_type == 'read_receipt':
                await self.handle_read_receipt(text_data_json)

        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'error': 'Invalid JSON format'
            }))

    async def handle_chat_message(self, data):
        """Handle chat message."""
        content = data.get('content', '').strip()
        reply_to_id = data.get('reply_to')

        if not content:
            return

        # Save message to database
        message = await self.save_message(content, reply_to_id)

        if message:
            # Send message to room group
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'chat_message',
                    'message': {
                        'id': str(message.id),
                        'content': message.content,
                        'sender': message.sender.username,
                        'sender_id': message.sender.id,
                        'created_at': message.created_at.isoformat(),
                        'reply_to': str(message.reply_to.id) if message.reply_to else None,
                    }
                }
            )

    async def handle_typing(self, data):
        """Handle typing indicator."""
        is_typing = data.get('is_typing', False)
        
        if is_typing:
            await self.set_typing()
        else:
            await self.stop_typing()

        # Send typing indicator to room group (excluding sender)
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'typing_indicator',
                'user': self.user.username,
                'user_id': self.user.id,
                'is_typing': is_typing,
            }
        )

    async def handle_read_receipt(self, data):
        """Handle read receipt."""
        await self.mark_as_read()

    # WebSocket message handlers
    async def chat_message(self, event):
        """Send chat message to WebSocket."""
        await self.send(text_data=json.dumps({
            'type': 'message',
            'message': event['message']
        }))

    async def user_join(self, event):
        """Send user join notification to WebSocket."""
        if event['user_id'] != self.user.id:  # Don't send to self
            await self.send(text_data=json.dumps({
                'type': 'user_join',
                'user': event['user'],
                'user_id': event['user_id'],
            }))

    async def user_leave(self, event):
        """Send user leave notification to WebSocket."""
        if event['user_id'] != self.user.id:  # Don't send to self
            await self.send(text_data=json.dumps({
                'type': 'user_leave',
                'user': event['user'],
                'user_id': event['user_id'],
            }))

    async def typing_indicator(self, event):
        """Send typing indicator to WebSocket."""
        if event['user_id'] != self.user.id:  # Don't send to self
            await self.send(text_data=json.dumps({
                'type': 'typing',
                'user': event['user'],
                'user_id': event['user_id'],
                'is_typing': event['is_typing'],
            }))

    # Database operations
    @database_sync_to_async
    def is_participant(self):
        """Check if user is a participant in the chat room."""
        try:
            chat_room = ChatRoom.objects.get(id=self.room_id)
            return chat_room.participants.filter(id=self.user.id).exists()
        except ChatRoom.DoesNotExist:
            return False

    @database_sync_to_async
    def save_message(self, content, reply_to_id=None):
        """Save chat message to database."""
        try:
            chat_room = ChatRoom.objects.get(id=self.room_id)
            
            reply_to = None
            if reply_to_id:
                try:
                    reply_to = ChatMessage.objects.get(id=reply_to_id)
                except ChatMessage.DoesNotExist:
                    pass

            message = ChatMessage.objects.create(
                chat_room=chat_room,
                sender=self.user,
                content=content,
                reply_to=reply_to
            )
            return message
        except ChatRoom.DoesNotExist:
            return None

    @database_sync_to_async
    def set_user_online(self):
        """Set user status as online."""
        status, created = UserOnlineStatus.objects.get_or_create(
            user=self.user,
            defaults={'status': 'online'}
        )
        if not created:
            status.set_online()

    @database_sync_to_async
    def set_user_offline(self):
        """Set user status as offline."""
        try:
            status = UserOnlineStatus.objects.get(user=self.user)
            status.set_offline()
        except UserOnlineStatus.DoesNotExist:
            pass

    @database_sync_to_async
    def set_typing(self):
        """Set user as typing in this chat room."""
        try:
            chat_room = ChatRoom.objects.get(id=self.room_id)
            status, created = UserOnlineStatus.objects.get_or_create(
                user=self.user,
                defaults={'status': 'online'}
            )
            status.set_typing(chat_room)
        except ChatRoom.DoesNotExist:
            pass

    @database_sync_to_async
    def stop_typing(self):
        """Stop typing indicator."""
        try:
            status = UserOnlineStatus.objects.get(user=self.user)
            status.stop_typing()
        except UserOnlineStatus.DoesNotExist:
            pass

    @database_sync_to_async
    def mark_as_read(self):
        """Mark messages as read for this user."""
        try:
            chat_room = ChatRoom.objects.get(id=self.room_id)
            participant = ChatParticipant.objects.get(
                chat_room=chat_room,
                user=self.user
            )
            participant.mark_as_read()
        except (ChatRoom.DoesNotExist, ChatParticipant.DoesNotExist):
            pass


class NotificationConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for real-time notifications.
    """

    async def connect(self):
        """Handle WebSocket connection."""
        self.user = self.scope['user']

        if not self.user.is_authenticated:
            await self.close()
            return

        self.user_group_name = f'user_{self.user.id}'

        # Join user group
        await self.channel_layer.group_add(
            self.user_group_name,
            self.channel_name
        )

        await self.accept()

    async def disconnect(self, close_code):
        """Handle WebSocket disconnection."""
        if hasattr(self, 'user_group_name'):
            await self.channel_layer.group_discard(
                self.user_group_name,
                self.channel_name
            )

    async def notification(self, event):
        """Send notification to WebSocket."""
        await self.send(text_data=json.dumps({
            'type': 'notification',
            'notification': event['notification']
        }))


class TypingConsumer(AsyncWebsocketConsumer):
    """
    Simplified WebSocket consumer for typing indicators only.
    """

    async def connect(self):
        """Handle WebSocket connection."""
        self.room_id = self.scope['url_route']['kwargs']['room_id']
        self.room_group_name = f'typing_{self.room_id}'
        self.user = self.scope['user']

        if not self.user.is_authenticated:
            await self.close()
            return

        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )

        await self.accept()

    async def disconnect(self, close_code):
        """Handle WebSocket disconnection."""
        if hasattr(self, 'room_group_name'):
            await self.channel_layer.group_discard(
                self.room_group_name,
                self.channel_name
            )

    async def receive(self, text_data):
        """Handle typing indicator."""
        try:
            data = json.loads(text_data)
            is_typing = data.get('is_typing', False)

            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'typing_update',
                    'user': self.user.username,
                    'user_id': self.user.id,
                    'is_typing': is_typing,
                }
            )
        except json.JSONDecodeError:
            pass

    async def typing_update(self, event):
        """Send typing update to WebSocket."""
        if event['user_id'] != self.user.id:  # Don't send to self
            await self.send(text_data=json.dumps({
                'user': event['user'],
                'user_id': event['user_id'],
                'is_typing': event['is_typing'],
            }))
