{% extends 'base.html' %}

{% block title %}Register - BlogApp{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="card">
            <div class="card-header text-center">
                <h4 class="mb-0"><i class="fas fa-user-plus"></i> Create Account</h4>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    {% if form.errors %}
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> 
                        Please correct the errors below.
                    </div>
                    {% endif %}
                    
                    <div class="mb-3">
                        <label for="{{ form.username.id_for_label }}" class="form-label">
                            <i class="fas fa-user"></i> Username
                        </label>
                        <input type="text" 
                               class="form-control {% if form.username.errors %}is-invalid{% endif %}" 
                               id="{{ form.username.id_for_label }}" 
                               name="{{ form.username.name }}" 
                               value="{{ form.username.value|default:'' }}"
                               required>
                        {% if form.username.errors %}
                        <div class="invalid-feedback">
                            {{ form.username.errors.0 }}
                        </div>
                        {% endif %}
                        <div class="form-text">Choose a unique username (letters, numbers, and underscores only)</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.email.id_for_label }}" class="form-label">
                            <i class="fas fa-envelope"></i> Email
                        </label>
                        <input type="email" 
                               class="form-control {% if form.email.errors %}is-invalid{% endif %}" 
                               id="{{ form.email.id_for_label }}" 
                               name="{{ form.email.name }}" 
                               value="{{ form.email.value|default:'' }}"
                               required>
                        {% if form.email.errors %}
                        <div class="invalid-feedback">
                            {{ form.email.errors.0 }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.password1.id_for_label }}" class="form-label">
                            <i class="fas fa-lock"></i> Password
                        </label>
                        <input type="password" 
                               class="form-control {% if form.password1.errors %}is-invalid{% endif %}" 
                               id="{{ form.password1.id_for_label }}" 
                               name="{{ form.password1.name }}" 
                               required>
                        {% if form.password1.errors %}
                        <div class="invalid-feedback">
                            {{ form.password1.errors.0 }}
                        </div>
                        {% endif %}
                        <div class="form-text">Password must be at least 8 characters long</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.password2.id_for_label }}" class="form-label">
                            <i class="fas fa-lock"></i> Confirm Password
                        </label>
                        <input type="password" 
                               class="form-control {% if form.password2.errors %}is-invalid{% endif %}" 
                               id="{{ form.password2.id_for_label }}" 
                               name="{{ form.password2.name }}" 
                               required>
                        {% if form.password2.errors %}
                        <div class="invalid-feedback">
                            {{ form.password2.errors.0 }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-user-plus"></i> Create Account
                        </button>
                    </div>
                </form>
                
                <hr class="my-4">
                
                <div class="text-center">
                    <p class="text-muted mb-2">Already have an account?</p>
                    <a href="{% url 'login' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-sign-in-alt"></i> Login
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Benefits Section -->
        <div class="card mt-3">
            <div class="card-body">
                <h6 class="card-title"><i class="fas fa-star"></i> Why Join BlogApp?</h6>
                <ul class="list-unstyled mb-0">
                    <li><i class="fas fa-check text-success me-2"></i>Share your thoughts with the world</li>
                    <li><i class="fas fa-check text-success me-2"></i>Connect with like-minded people</li>
                    <li><i class="fas fa-check text-success me-2"></i>Discover trending topics and discussions</li>
                    <li><i class="fas fa-check text-success me-2"></i>Free to use, no hidden fees</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
