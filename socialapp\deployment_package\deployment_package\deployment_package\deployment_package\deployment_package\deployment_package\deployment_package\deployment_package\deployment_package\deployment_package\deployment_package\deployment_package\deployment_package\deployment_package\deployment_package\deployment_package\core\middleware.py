# type: ignore
"""
Custom middleware for the social blogging platform.
"""

from django.http import HttpResponseForbidden
from django.utils.deprecation import MiddlewareMixin
from django.core.cache import cache
from django.conf import settings
import time
import logging

logger = logging.getLogger(__name__)


class SecurityHeadersMiddleware(MiddlewareMixin):
    """Add security headers to all responses."""
    
    def process_response(self, request, response):
        """Add security headers."""
        # Prevent clickjacking
        response['X-Frame-Options'] = 'DENY'
        
        # Prevent MIME type sniffing
        response['X-Content-Type-Options'] = 'nosniff'
        
        # Enable XSS protection
        response['X-XSS-Protection'] = '1; mode=block'
        
        # Referrer policy
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # Content Security Policy
        if not settings.DEBUG:
            csp = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; "
                "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; "
                "img-src 'self' data: https:; "
                "font-src 'self' https://cdnjs.cloudflare.com; "
                "connect-src 'self'; "
                "media-src 'self'; "
                "object-src 'none'; "
                "base-uri 'self'; "
                "form-action 'self';"
            )
            response['Content-Security-Policy'] = csp
        
        return response


class RequestLoggingMiddleware(MiddlewareMixin):
    """Log suspicious requests."""
    
    def process_request(self, request):
        """Log request details for security monitoring."""
        # Log suspicious patterns
        suspicious_patterns = [
            'admin', 'wp-admin', 'phpmyadmin', '.php', '.asp', '.jsp',
            'eval(', 'script>', '<iframe', 'javascript:', 'vbscript:',
            'onload=', 'onerror=', 'onclick=', 'onmouseover='
        ]
        
        path = request.get_full_path().lower()
        user_agent = request.META.get('HTTP_USER_AGENT', '').lower()
        
        for pattern in suspicious_patterns:
            if pattern in path or pattern in user_agent:
                logger.warning(
                    f"Suspicious request detected: {request.method} {path} "
                    f"from {request.META.get('REMOTE_ADDR')} "
                    f"User-Agent: {user_agent}"
                )
                break
        
        return None


class RateLimitMiddleware(MiddlewareMixin):
    """Simple rate limiting middleware."""
    
    def process_request(self, request):
        """Check rate limits."""
        if settings.DEBUG:
            return None  # Skip rate limiting in debug mode
        
        # Get client IP
        ip = self.get_client_ip(request)
        
        # Rate limit key
        cache_key = f"rate_limit:{ip}"
        
        # Get current request count
        current_requests = cache.get(cache_key, 0)
        
        # Rate limit: 100 requests per minute
        if current_requests >= 100:
            logger.warning(f"Rate limit exceeded for IP: {ip}")
            return HttpResponseForbidden("Rate limit exceeded. Please try again later.")
        
        # Increment counter
        cache.set(cache_key, current_requests + 1, 60)  # 60 seconds
        
        return None
    
    def get_client_ip(self, request):
        """Get the client's IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class FileUploadSecurityMiddleware(MiddlewareMixin):
    """Security middleware for file uploads."""
    
    DANGEROUS_EXTENSIONS = [
        '.php', '.php3', '.php4', '.php5', '.phtml',
        '.asp', '.aspx', '.jsp', '.jspx',
        '.py', '.pl', '.cgi', '.sh', '.bat',
        '.exe', '.com', '.scr', '.msi',
        '.js', '.vbs', '.jar'
    ]
    
    def process_request(self, request):
        """Check uploaded files for security issues."""
        if request.method == 'POST' and request.FILES:
            for field_name, uploaded_file in request.FILES.items():
                # Check file extension
                file_name = uploaded_file.name.lower()
                for ext in self.DANGEROUS_EXTENSIONS:
                    if file_name.endswith(ext):
                        logger.warning(
                            f"Dangerous file upload attempt: {uploaded_file.name} "
                            f"from {request.META.get('REMOTE_ADDR')}"
                        )
                        return HttpResponseForbidden("File type not allowed.")
                
                # Check file size (100MB limit)
                if uploaded_file.size > 100 * 1024 * 1024:
                    logger.warning(
                        f"Large file upload attempt: {uploaded_file.size} bytes "
                        f"from {request.META.get('REMOTE_ADDR')}"
                    )
                    return HttpResponseForbidden("File too large.")
                
                # Check for embedded scripts in image files
                if uploaded_file.content_type and uploaded_file.content_type.startswith('image/'):
                    # Read first 1KB to check for script tags
                    uploaded_file.seek(0)
                    content_sample = uploaded_file.read(1024).decode('utf-8', errors='ignore')
                    uploaded_file.seek(0)  # Reset file pointer
                    
                    script_patterns = ['<script', '<?php', '<%', 'javascript:', 'vbscript:']
                    for pattern in script_patterns:
                        if pattern in content_sample.lower():
                            logger.warning(
                                f"Script detected in image file: {uploaded_file.name} "
                                f"from {request.META.get('REMOTE_ADDR')}"
                            )
                            return HttpResponseForbidden("Invalid file content.")
        
        return None


class MaintenanceModeMiddleware(MiddlewareMixin):
    """Middleware to enable maintenance mode."""
    
    def process_request(self, request):
        """Check if site is in maintenance mode."""
        maintenance_mode = getattr(settings, 'MAINTENANCE_MODE', False)
        
        if maintenance_mode:
            # Allow superusers to access the site
            if request.user.is_authenticated and request.user.is_superuser:
                return None
            
            # Allow access to admin and maintenance pages
            if request.path.startswith('/admin/') or request.path.startswith('/maintenance/'):
                return None
            
            # Return maintenance page for everyone else
            from django.shortcuts import render
            return render(request, 'maintenance.html', status=503)
        
        return None
