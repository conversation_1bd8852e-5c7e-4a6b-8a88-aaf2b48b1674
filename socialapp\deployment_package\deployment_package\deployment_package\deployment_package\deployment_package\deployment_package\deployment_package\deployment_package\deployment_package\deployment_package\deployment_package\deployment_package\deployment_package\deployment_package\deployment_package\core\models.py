# type: ignore
"""
Core models for the blogging platform.

This module contains the main data models for the social blogging application,
including User profiles, Posts, Comments, Reactions, Hashtags, and social features.
"""

from django.contrib.auth.models import User
from django.db import models
from django.utils.text import slugify
from django.contrib.auth.models import Group as AuthGroup
from django.core.validators import MinLengthValidator, MaxLengthValidator, FileExtensionValidator
from django.core.exceptions import ValidationError
from django.urls import reverse
from django.utils import timezone
from django.conf import settings
import re
import os
import uuid


def validate_image_file(value):
    """Validate uploaded image files."""
    if value:
        # Check file size (5MB limit)
        if value.size > 5 * 1024 * 1024:
            raise ValidationError('Image file size must be less than 5MB.')

        # Check file extension
        ext = os.path.splitext(value.name)[1].lower()
        valid_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
        if ext not in valid_extensions:
            raise ValidationError('Only JPEG, PNG, GIF, and WebP images are allowed.')


def validate_video_file(value):
    """Validate uploaded video files."""
    if value:
        # Check file size (100MB limit)
        if value.size > 100 * 1024 * 1024:
            raise ValidationError('Video file size must be less than 100MB.')

        # Check file extension
        ext = os.path.splitext(value.name)[1].lower()
        valid_extensions = ['.mp4', '.webm', '.ogg']
        if ext not in valid_extensions:
            raise ValidationError('Only MP4, WebM, and OGG videos are allowed.')


class TimeStampedModel(models.Model):
    """Abstract base model that provides self-updating created_at and updated_at fields."""
    
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True, db_index=True)

    class Meta:
        abstract = True


class Profile(TimeStampedModel):
    """
    Extended user profile model with social blogging features.
    """
    
    user = models.OneToOneField(
        User, 
        on_delete=models.CASCADE, 
        related_name='profile',
        help_text="The user this profile belongs to"
    )
    bio = models.TextField(
        blank=True, 
        max_length=500,
        validators=[
            MinLengthValidator(10, message="Bio must be at least 10 characters long."),
            MaxLengthValidator(500, message="Bio cannot exceed 500 characters.")
        ],
        help_text="User's biography (max 500 characters)"
    )
    avatar = models.ImageField(
        upload_to='avatars/%Y/%m/%d/',
        blank=True,
        null=True,
        validators=[validate_image_file],
        help_text="User's profile picture (max 5MB)"
    )
    cover_photo = models.ImageField(
        upload_to='covers/%Y/%m/%d/',
        blank=True,
        null=True,
        validators=[validate_image_file],
        help_text="User's cover photo (max 5MB)"
    )
    date_of_birth = models.DateField(
        blank=True, 
        null=True,
        help_text="User's date of birth"
    )
    location = models.CharField(
        max_length=100, 
        blank=True,
        help_text="User's location"
    )
    website = models.URLField(
        blank=True,
        help_text="User's personal website"
    )
    is_verified = models.BooleanField(
        default=False,
        help_text="Whether the user account is verified"
    )
    followers_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of followers"
    )
    following_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of users being followed"
    )
    posts_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of posts by this user"
    )

    class Meta:
        verbose_name = "User Profile"
        verbose_name_plural = "User Profiles"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username}'s Profile"

    def get_absolute_url(self):
        """Return the URL to access a particular profile."""
        return reverse('profile', kwargs={'username': self.user.username})

    def get_avatar_url(self):
        """Return the avatar URL or a default avatar."""
        if self.avatar:
            return self.avatar.url
        return '/static/images/default-avatar.png'

    def get_cover_url(self):
        """Return the cover photo URL or a default cover."""
        if self.cover_photo:
            return self.cover_photo.url
        return '/static/images/default-cover.jpg'

    def update_counts(self):
        """Update follower, following, and post counts."""
        self.followers_count = self.user.followers.count()
        self.following_count = self.user.following.count()
        self.posts_count = self.user.posts.filter(is_deleted=False).count()
        self.save(update_fields=['followers_count', 'following_count', 'posts_count'])


class Follow(TimeStampedModel):
    """
    Follow relationship between users.
    """
    
    follower = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='following',
        help_text="User who is following"
    )
    following = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='followers',
        help_text="User being followed"
    )

    class Meta:
        unique_together = ('follower', 'following')
        verbose_name = "Follow"
        verbose_name_plural = "Follows"
        ordering = ['-created_at']
        constraints = [
            models.CheckConstraint(
                check=~models.Q(follower=models.F('following')),
                name='prevent_self_follow'
            )
        ]

    def __str__(self):
        return f"{self.follower.username} follows {self.following.username}"


class Hashtag(TimeStampedModel):
    """
    Hashtag model for categorizing posts.
    """
    
    name = models.CharField(
        max_length=50,
        unique=True,
        help_text="Hashtag name (without #)"
    )
    slug = models.SlugField(
        unique=True,
        max_length=50,
        help_text="URL-friendly version of the hashtag"
    )
    description = models.TextField(
        blank=True,
        max_length=500,
        help_text="Description of the hashtag"
    )
    posts_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of posts using this hashtag"
    )

    class Meta:
        verbose_name = "Hashtag"
        verbose_name_plural = "Hashtags"
        ordering = ['-posts_count', '-created_at']
        indexes = [
            models.Index(fields=['slug']),
            models.Index(fields=['name']),
        ]

    def __str__(self):
        return f"#{self.name}"

    def save(self, *args, **kwargs):
        """Generate slug if not provided."""
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        """Return the URL to access this hashtag."""
        return reverse('hashtag_detail', kwargs={'slug': self.slug})

    def update_posts_count(self):
        """Update the posts count."""
        self.posts_count = self.posts.filter(is_deleted=False).count()
        self.save(update_fields=['posts_count'])


class Post(TimeStampedModel):
    """
    Post model for user-generated content.
    
    Represents the main content unit in the blogging platform.
    """
    
    user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE,
        related_name='posts',
        help_text="User who created the post"
    )
    content = models.TextField(
        validators=[
            MinLengthValidator(1, message="Post content cannot be empty."),
            MaxLengthValidator(10000, message="Post cannot exceed 10000 characters.")
        ],
        help_text="The content of the post"
    )
    image = models.ImageField(
        upload_to='posts/%Y/%m/%d/',
        blank=True,
        null=True,
        validators=[validate_image_file],
        help_text="Image attached to the post (max 10MB)"
    )
    video = models.FileField(
        upload_to='posts/videos/%Y/%m/%d/',
        blank=True,
        null=True,
        validators=[validate_video_file],
        help_text="Video attached to the post (max 100MB)"
    )
    hashtags = models.ManyToManyField(
        Hashtag,
        related_name='posts',
        blank=True,
        help_text="Hashtags associated with this post"
    )
    is_public = models.BooleanField(
        default=True,
        help_text="Whether the post is public or private"
    )
    is_edited = models.BooleanField(
        default=False,
        help_text="Whether this post has been edited"
    )
    edited_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the post was last edited"
    )
    is_deleted = models.BooleanField(
        default=False,
        help_text="Whether this post has been deleted"
    )
    likes_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of likes on this post"
    )
    comments_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of comments on this post"
    )
    shares_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of times this post has been shared"
    )
    views_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of views on this post"
    )

    class Meta:
        verbose_name = "Post"
        verbose_name_plural = "Posts"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['is_public', '-created_at']),
            models.Index(fields=['is_deleted', '-created_at']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.content[:50]}..."

    def save(self, *args, **kwargs):
        """Handle editing timestamp and extract hashtags."""
        if self.pk:  # If this is an update
            self.is_edited = True
            self.edited_at = timezone.now()
        
        # Extract hashtags from content
        hashtag_pattern = r'#(\w+)'
        hashtags_in_content = re.findall(hashtag_pattern, self.content)
        
        super().save(*args, **kwargs)
        
        # Add hashtags to the post
        for hashtag_name in hashtags_in_content:
            hashtag, _ = Hashtag.objects.get_or_create(
                name=hashtag_name.lower(),
                defaults={'description': f'Posts tagged with #{hashtag_name}'}
            )
            self.hashtags.add(hashtag)

    def get_absolute_url(self):
        """Return the URL to access this post."""
        return reverse('post_detail', kwargs={'pk': self.pk})

    def update_counts(self):
        """Update like, comment, and share counts."""
        self.likes_count = self.likes.count()
        self.comments_count = self.comments.filter(is_deleted=False).count()
        self.shares_count = self.shares.count()
        self.save(update_fields=['likes_count', 'comments_count', 'shares_count'])

    def is_liked_by(self, user):
        """Check if this post is liked by a specific user."""
        return self.likes.filter(user=user).exists()

    def is_bookmarked_by(self, user):
        """Check if this post is bookmarked by a specific user."""
        return self.bookmarks.filter(user=user).exists()

    def soft_delete(self):
        """Soft delete the post."""
        self.is_deleted = True
        self.save(update_fields=['is_deleted'])

    def get_hashtags_display(self):
        """Return hashtags as a formatted string."""
        return ' '.join([f'#{hashtag.name}' for hashtag in self.hashtags.all()])


class Comment(TimeStampedModel):
    """
    Comment model for post interactions.
    
    Represents comments on posts with nested replies.
    """
    
    post = models.ForeignKey(
        Post, 
        on_delete=models.CASCADE, 
        related_name='comments',
        help_text="Post this comment belongs to"
    )
    user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE,
        related_name='comments',
        help_text="User who wrote the comment"
    )
    content = models.TextField(
        validators=[
            MinLengthValidator(1, message="Comment cannot be empty."),
            MaxLengthValidator(1000, message="Comment cannot exceed 1000 characters.")
        ],
        help_text="The content of the comment"
    )
    parent = models.ForeignKey(
        'self', 
        null=True, 
        blank=True, 
        on_delete=models.CASCADE, 
        related_name='replies',
        help_text="Parent comment if this is a reply"
    )
    is_edited = models.BooleanField(
        default=False,
        help_text="Whether this comment has been edited"
    )
    edited_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the comment was last edited"
    )
    is_deleted = models.BooleanField(
        default=False,
        help_text="Whether this comment has been deleted"
    )
    likes_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of likes on this comment"
    )

    class Meta:
        verbose_name = "Comment"
        verbose_name_plural = "Comments"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['post', '-created_at']),
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['parent', '-created_at']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.content[:50]}..."

    def save(self, *args, **kwargs):
        """Handle editing timestamp."""
        if self.pk:  # If this is an update
            self.is_edited = True
            self.edited_at = timezone.now()
        super().save(*args, **kwargs)

    def update_likes_count(self):
        """Update the likes count."""
        self.likes_count = self.likes.count()
        self.save(update_fields=['likes_count'])

    def is_liked_by(self, user):
        """Check if this comment is liked by a specific user."""
        return self.likes.filter(user=user).exists()

    def soft_delete(self):
        """Soft delete the comment."""
        self.is_deleted = True
        self.save(update_fields=['is_deleted'])


class Reaction(TimeStampedModel):
    """
    Reaction model for post and comment interactions.
    
    Supports different types of reactions (like, love, laugh, etc.).
    """
    
    REACTION_TYPES = [
        ('like', 'Like'),
        ('love', 'Love'),
        ('laugh', 'Laugh'),
        ('wow', 'Wow'),
        ('sad', 'Sad'),
        ('angry', 'Angry'),
    ]
    
    user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE,
        related_name='reactions',
        help_text="User who reacted"
    )
    reaction_type = models.CharField(
        max_length=10,
        choices=REACTION_TYPES,
        default='like',
        help_text="Type of reaction"
    )
    post = models.ForeignKey(
        Post,
        on_delete=models.CASCADE,
        related_name='reactions',
        null=True,
        blank=True,
        help_text="Post being reacted to"
    )
    comment = models.ForeignKey(
        Comment,
        on_delete=models.CASCADE,
        related_name='reactions',
        null=True,
        blank=True,
        help_text="Comment being reacted to"
    )

    class Meta:
        unique_together = [
            ('user', 'post'),
            ('user', 'comment'),
        ]
        verbose_name = "Reaction"
        verbose_name_plural = "Reactions"
        ordering = ['-created_at']

    def __str__(self):
        target = self.post or self.comment
        return f"{self.user.username} {self.reaction_type}s {target}"

    def save(self, *args, **kwargs):
        """Update counts when saving."""
        super().save(*args, **kwargs)
        if self.post:
            self.post.update_counts()
        elif self.comment:
            self.comment.update_likes_count()

    def delete(self, *args, **kwargs):
        """Update counts when deleting."""
        super().delete(*args, **kwargs)
        if self.post:
            self.post.update_counts()
        elif self.comment:
            self.comment.update_likes_count()


class Bookmark(TimeStampedModel):
    """
    Bookmark model for saving posts.
    """
    
    user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE,
        related_name='bookmarks',
        help_text="User who bookmarked"
    )
    post = models.ForeignKey(
        Post, 
        on_delete=models.CASCADE,
        related_name='bookmarks',
        help_text="Post being bookmarked"
    )

    class Meta:
        unique_together = ('user', 'post')
        verbose_name = "Bookmark"
        verbose_name_plural = "Bookmarks"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} bookmarked {self.post}"


class Share(TimeStampedModel):
    """
    Share model for reposting content.
    """
    
    user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE,
        related_name='shares',
        help_text="User who shared"
    )
    original_post = models.ForeignKey(
        Post, 
        on_delete=models.CASCADE,
        related_name='shares',
        help_text="Original post being shared"
    )
    share_text = models.TextField(
        blank=True,
        max_length=1000,
        help_text="Additional text when sharing"
    )

    class Meta:
        verbose_name = "Share"
        verbose_name_plural = "Shares"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} shared {self.original_post}"


class Notification(TimeStampedModel):
    """
    Notification model for user notifications.
    """
    
    NOTIFICATION_TYPES = [
        ('follow', 'Follow'),
        ('like', 'Like'),
        ('comment', 'Comment'),
        ('share', 'Share'),
        ('mention', 'Mention'),
    ]
    
    recipient = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='notifications',
        help_text="User receiving the notification"
    )
    sender = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sent_notifications',
        help_text="User who triggered the notification"
    )
    notification_type = models.CharField(
        max_length=20,
        choices=NOTIFICATION_TYPES,
        help_text="Type of notification"
    )
    post = models.ForeignKey(
        Post,
        on_delete=models.CASCADE,
        related_name='notifications',
        null=True,
        blank=True,
        help_text="Related post"
    )
    comment = models.ForeignKey(
        Comment,
        on_delete=models.CASCADE,
        related_name='notifications',
        null=True,
        blank=True,
        help_text="Related comment"
    )
    is_read = models.BooleanField(
        default=False,
        help_text="Whether the notification has been read"
    )

    class Meta:
        verbose_name = "Notification"
        verbose_name_plural = "Notifications"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['recipient', 'is_read']),
        ]

    def __str__(self):
        return f"{self.sender.username} {self.notification_type} - {self.recipient.username}"


# ============================================================================
# CHAT & MESSAGING MODELS
# ============================================================================

class ChatRoom(TimeStampedModel):
    """
    Chat room model for group conversations.
    """

    ROOM_TYPES = [
        ('private', 'Private Chat'),
        ('group', 'Group Chat'),
        ('public', 'Public Chat'),
    ]

    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        help_text="Unique identifier for the chat room"
    )
    name = models.CharField(
        max_length=100,
        blank=True,
        help_text="Name of the chat room (for group chats)"
    )
    description = models.TextField(
        blank=True,
        max_length=500,
        help_text="Description of the chat room"
    )
    room_type = models.CharField(
        max_length=10,
        choices=ROOM_TYPES,
        default='private',
        help_text="Type of chat room"
    )
    participants = models.ManyToManyField(
        User,
        through='ChatParticipant',
        related_name='chat_rooms',
        help_text="Users participating in this chat"
    )
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='created_chat_rooms',
        help_text="User who created this chat room"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Whether the chat room is active"
    )
    last_message = models.ForeignKey(
        'ChatMessage',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='+',
        help_text="Last message in this chat room"
    )
    last_activity = models.DateTimeField(
        auto_now=True,
        help_text="Last activity in this chat room"
    )

    class Meta:
        verbose_name = "Chat Room"
        verbose_name_plural = "Chat Rooms"
        ordering = ['-last_activity']
        indexes = [
            models.Index(fields=['room_type', '-last_activity']),
            models.Index(fields=['is_active', '-last_activity']),
        ]

    def __str__(self):
        if self.name:
            return self.name
        elif self.room_type == 'private':
            participants = list(self.participants.all()[:2])
            if len(participants) == 2:
                return f"{participants[0].username} & {participants[1].username}"
        return f"Chat Room {self.id}"

    def get_absolute_url(self):
        """Return the URL to access this chat room."""
        return reverse('chat_room', kwargs={'room_id': self.id})

    def get_other_participant(self, user):
        """Get the other participant in a private chat."""
        if self.room_type == 'private':
            return self.participants.exclude(id=user.id).first()
        return None

    def add_participant(self, user, role='member'):
        """Add a participant to the chat room."""
        participant, created = ChatParticipant.objects.get_or_create(
            chat_room=self,
            user=user,
            defaults={'role': role}
        )
        return participant

    def remove_participant(self, user):
        """Remove a participant from the chat room."""
        ChatParticipant.objects.filter(chat_room=self, user=user).delete()

    def get_unread_count(self, user):
        """Get unread message count for a user."""
        participant = self.participants_details.filter(user=user).first()
        if participant and participant.last_read_at:
            return self.messages.filter(
                created_at__gt=participant.last_read_at
            ).exclude(sender=user).count()
        return self.messages.exclude(sender=user).count()


class ChatParticipant(TimeStampedModel):
    """
    Through model for chat room participants with additional metadata.
    """

    PARTICIPANT_ROLES = [
        ('admin', 'Admin'),
        ('moderator', 'Moderator'),
        ('member', 'Member'),
    ]

    chat_room = models.ForeignKey(
        ChatRoom,
        on_delete=models.CASCADE,
        related_name='participants_details',
        help_text="Chat room"
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='chat_participations',
        help_text="Participating user"
    )
    role = models.CharField(
        max_length=10,
        choices=PARTICIPANT_ROLES,
        default='member',
        help_text="Role of the participant"
    )
    joined_at = models.DateTimeField(
        auto_now_add=True,
        help_text="When the user joined the chat"
    )
    last_read_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Last time the user read messages"
    )
    is_muted = models.BooleanField(
        default=False,
        help_text="Whether notifications are muted for this user"
    )
    is_pinned = models.BooleanField(
        default=False,
        help_text="Whether this chat is pinned for the user"
    )

    class Meta:
        unique_together = ('chat_room', 'user')
        verbose_name = "Chat Participant"
        verbose_name_plural = "Chat Participants"
        ordering = ['-joined_at']

    def __str__(self):
        return f"{self.user.username} in {self.chat_room}"

    def mark_as_read(self):
        """Mark all messages as read for this participant."""
        self.last_read_at = timezone.now()
        self.save(update_fields=['last_read_at'])


class ChatMessage(TimeStampedModel):
    """
    Chat message model for individual messages in chat rooms.
    """

    MESSAGE_TYPES = [
        ('text', 'Text'),
        ('image', 'Image'),
        ('file', 'File'),
        ('system', 'System'),
    ]

    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        help_text="Unique identifier for the message"
    )
    chat_room = models.ForeignKey(
        ChatRoom,
        on_delete=models.CASCADE,
        related_name='messages',
        help_text="Chat room this message belongs to"
    )
    sender = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sent_messages',
        null=True,
        blank=True,
        help_text="User who sent the message (null for system messages)"
    )
    message_type = models.CharField(
        max_length=10,
        choices=MESSAGE_TYPES,
        default='text',
        help_text="Type of message"
    )
    content = models.TextField(
        blank=True,
        help_text="Text content of the message"
    )
    attachment = models.FileField(
        upload_to='chat_attachments/%Y/%m/%d/',
        blank=True,
        null=True,
        help_text="File attachment"
    )
    reply_to = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='replies',
        help_text="Message this is replying to"
    )
    is_edited = models.BooleanField(
        default=False,
        help_text="Whether this message has been edited"
    )
    edited_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the message was last edited"
    )
    is_deleted = models.BooleanField(
        default=False,
        help_text="Whether this message has been deleted"
    )
    deleted_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the message was deleted"
    )

    class Meta:
        verbose_name = "Chat Message"
        verbose_name_plural = "Chat Messages"
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['chat_room', 'created_at']),
            models.Index(fields=['sender', 'created_at']),
            models.Index(fields=['is_deleted', 'created_at']),
        ]

    def __str__(self):
        if self.message_type == 'system':
            return f"System: {self.content[:50]}..."
        return f"{self.sender.username}: {self.content[:50]}..."

    def save(self, *args, **kwargs):
        """Handle editing timestamp and update chat room last activity."""
        if self.pk and not self.is_deleted:  # If this is an update
            self.is_edited = True
            self.edited_at = timezone.now()

        super().save(*args, **kwargs)

        # Update chat room's last message and activity
        if not self.is_deleted:
            self.chat_room.last_message = self
            self.chat_room.last_activity = self.created_at
            self.chat_room.save(update_fields=['last_message', 'last_activity'])

    def soft_delete(self):
        """Soft delete the message."""
        self.is_deleted = True
        self.deleted_at = timezone.now()
        self.save(update_fields=['is_deleted', 'deleted_at'])

    def get_attachment_url(self):
        """Get the URL for the attachment."""
        if self.attachment:
            return self.attachment.url
        return None

    def get_attachment_filename(self):
        """Get the filename of the attachment."""
        if self.attachment:
            return os.path.basename(self.attachment.name)
        return None


class UserOnlineStatus(TimeStampedModel):
    """
    Track user online status for real-time features.
    """

    STATUS_CHOICES = [
        ('online', 'Online'),
        ('away', 'Away'),
        ('busy', 'Busy'),
        ('offline', 'Offline'),
    ]

    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='online_status',
        help_text="User whose status is being tracked"
    )
    status = models.CharField(
        max_length=10,
        choices=STATUS_CHOICES,
        default='offline',
        help_text="Current online status"
    )
    last_seen = models.DateTimeField(
        auto_now=True,
        help_text="Last time the user was seen online"
    )
    is_typing_in = models.ForeignKey(
        ChatRoom,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='typing_users',
        help_text="Chat room the user is currently typing in"
    )

    class Meta:
        verbose_name = "User Online Status"
        verbose_name_plural = "User Online Statuses"

    def __str__(self):
        return f"{self.user.username} - {self.status}"

    def set_online(self):
        """Set user status to online."""
        self.status = 'online'
        self.last_seen = timezone.now()
        self.save(update_fields=['status', 'last_seen'])

    def set_offline(self):
        """Set user status to offline."""
        self.status = 'offline'
        self.is_typing_in = None
        self.save(update_fields=['status', 'is_typing_in'])

    def set_typing(self, chat_room):
        """Set user as typing in a specific chat room."""
        self.is_typing_in = chat_room
        self.save(update_fields=['is_typing_in'])

    def stop_typing(self):
        """Stop typing indicator."""
        self.is_typing_in = None
        self.save(update_fields=['is_typing_in'])

    @property
    def is_online(self):
        """Check if user is currently online."""
        return self.status == 'online'

    @property
    def time_since_last_seen(self):
        """Get time since user was last seen."""
        return timezone.now() - self.last_seen
