#!/usr/bin/env python3
"""
Test script to verify like/comment AJAX and chat functionality fixes.
"""

import os
import sys
import django
from django.conf import settings

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'socialapp.settings')
django.setup()

def test_ajax_endpoints():
    """Test that AJAX endpoints are properly configured."""
    from django.urls import reverse
    from django.test import Client
    from django.contrib.auth.models import User
    from core.models import Post
    
    print("🧪 Testing AJAX endpoints...")
    
    # Create test client
    client = Client()
    
    # Create test user
    user, created = User.objects.get_or_create(
        username='testuser',
        defaults={'email': '<EMAIL>'}
    )
    if created:
        user.set_password('testpass123')
        user.save()
    
    # Login
    client.login(username='testuser', password='testpass123')
    
    # Create test post
    post = Post.objects.create(
        user=user,
        content="Test post for AJAX testing"
    )
    
    # Test like endpoint with AJAX
    response = client.post(
        reverse('toggle_reaction', args=[post.pk]),
        {'reaction_type': 'like'},
        HTTP_X_REQUESTED_WITH='XMLHttpRequest'
    )
    
    if response.status_code == 200:
        data = response.json()
        if 'success' in data:
            print("✅ Like AJAX endpoint working")
        else:
            print("❌ Like AJAX endpoint not returning proper JSON")
    else:
        print(f"❌ Like AJAX endpoint failed: {response.status_code}")
    
    # Test comment endpoint with AJAX
    response = client.post(
        reverse('add_comment', args=[post.pk]),
        {'content': 'Test comment'},
        HTTP_X_REQUESTED_WITH='XMLHttpRequest'
    )
    
    if response.status_code == 200:
        data = response.json()
        if 'success' in data:
            print("✅ Comment AJAX endpoint working")
        else:
            print("❌ Comment AJAX endpoint not returning proper JSON")
    else:
        print(f"❌ Comment AJAX endpoint failed: {response.status_code}")

def test_channel_layers():
    """Test channel layers configuration."""
    print("\n🧪 Testing Channel Layers...")
    
    try:
        from channels.layers import get_channel_layer
        channel_layer = get_channel_layer()
        
        if channel_layer:
            print(f"✅ Channel layer configured: {channel_layer.__class__.__name__}")
            
            # Test if it's Redis or in-memory
            if 'Redis' in channel_layer.__class__.__name__:
                print("✅ Using Redis channel layer (production ready)")
            else:
                print("⚠️  Using in-memory channel layer (fallback)")
        else:
            print("❌ No channel layer configured")
            
    except Exception as e:
        print(f"❌ Channel layer error: {e}")

def test_websocket_urls():
    """Test WebSocket URL patterns."""
    print("\n🧪 Testing WebSocket URLs...")
    
    try:
        from core.routing import websocket_urlpatterns
        
        if websocket_urlpatterns:
            print(f"✅ WebSocket URLs configured: {len(websocket_urlpatterns)} patterns")
            for pattern in websocket_urlpatterns:
                print(f"   - {pattern.pattern.regex.pattern}")
        else:
            print("❌ No WebSocket URLs configured")
            
    except Exception as e:
        print(f"❌ WebSocket URL error: {e}")

def main():
    """Run all tests."""
    print("🚀 Testing Django Social App Fixes\n")
    
    test_ajax_endpoints()
    test_channel_layers()
    test_websocket_urls()
    
    print("\n✅ Test completed!")
    print("\n📝 Next steps:")
    print("1. Deploy the updated code to Render")
    print("2. Test like/comment buttons on live site")
    print("3. Test real-time chat functionality")
    print("4. Check browser console for any WebSocket errors")

if __name__ == '__main__':
    main()
