from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.models import User
from .models import (
    Profile, Post, Comment, Reaction, Hashtag, Follow, 
    Bookmark, Share, Notification
)


class ProfileInline(admin.StackedInline):
    model = Profile
    can_delete = False
    verbose_name_plural = 'Profile'


class UserAdmin(BaseUserAdmin):
    inlines = (ProfileInline,)
    list_display = ('username', 'email', 'first_name', 'last_name', 'is_staff', 'get_followers_count', 'get_posts_count')
    list_filter = ('is_staff', 'is_superuser', 'is_active', 'groups')
    search_fields = ('username', 'first_name', 'last_name', 'email')
    
    def get_followers_count(self, obj):
        return obj.profile.followers_count
    get_followers_count.short_description = 'Followers'
    
    def get_posts_count(self, obj):
        return obj.profile.posts_count
    get_posts_count.short_description = 'Posts'


class ProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'bio', 'location', 'is_verified', 'followers_count', 'following_count', 'posts_count')
    list_filter = ('is_verified', 'date_of_birth')
    search_fields = ('user__username', 'user__email', 'bio', 'location')
    readonly_fields = ('followers_count', 'following_count', 'posts_count')
    fieldsets = (
        ('User Information', {
            'fields': ('user', 'bio', 'date_of_birth', 'location', 'website')
        }),
        ('Media', {
            'fields': ('avatar', 'cover_photo')
        }),
        ('Status', {
            'fields': ('is_verified',)
        }),
        ('Statistics', {
            'fields': ('followers_count', 'following_count', 'posts_count'),
            'classes': ('collapse',)
        }),
    )


class PostAdmin(admin.ModelAdmin):
    list_display = ('user', 'content_preview', 'is_public', 'is_edited', 'is_deleted', 'created_at', 'likes_count', 'comments_count')
    list_filter = ('is_public', 'is_edited', 'is_deleted', 'created_at', 'hashtags')
    search_fields = ('user__username', 'content')
    readonly_fields = ('likes_count', 'comments_count', 'shares_count', 'views_count', 'edited_at')
    filter_horizontal = ('hashtags',)
    date_hierarchy = 'created_at'
    
    def content_preview(self, obj):
        return obj.content[:100] + '...' if len(obj.content) > 100 else obj.content
    content_preview.short_description = 'Content'
    
    fieldsets = (
        ('Content', {
            'fields': ('user', 'content', 'image', 'video', 'hashtags')
        }),
        ('Settings', {
            'fields': ('is_public', 'is_edited', 'is_deleted')
        }),
        ('Statistics', {
            'fields': ('likes_count', 'comments_count', 'shares_count', 'views_count'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'edited_at'),
            'classes': ('collapse',)
        }),
    )


class CommentAdmin(admin.ModelAdmin):
    list_display = ('user', 'post', 'content_preview', 'parent', 'is_edited', 'is_deleted', 'created_at', 'likes_count')
    list_filter = ('is_edited', 'is_deleted', 'created_at')
    search_fields = ('user__username', 'content', 'post__content')
    readonly_fields = ('likes_count', 'edited_at')
    date_hierarchy = 'created_at'
    
    def content_preview(self, obj):
        return obj.content[:50] + '...' if len(obj.content) > 50 else obj.content
    content_preview.short_description = 'Content'


class ReactionAdmin(admin.ModelAdmin):
    list_display = ('user', 'reaction_type', 'get_target', 'created_at')
    list_filter = ('reaction_type', 'created_at')
    search_fields = ('user__username',)
    date_hierarchy = 'created_at'
    
    def get_target(self, obj):
        if obj.post:
            return f"Post: {obj.post.content[:50]}..."
        elif obj.comment:
            return f"Comment: {obj.comment.content[:50]}..."
        return "Unknown"
    get_target.short_description = 'Target'


class HashtagAdmin(admin.ModelAdmin):
    list_display = ('name', 'slug', 'posts_count', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('name', 'description')
    readonly_fields = ('posts_count', 'slug')
    prepopulated_fields = {'slug': ('name',)}


class FollowAdmin(admin.ModelAdmin):
    list_display = ('follower', 'following', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('follower__username', 'following__username')
    date_hierarchy = 'created_at'


class BookmarkAdmin(admin.ModelAdmin):
    list_display = ('user', 'post', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('user__username', 'post__content')
    date_hierarchy = 'created_at'


class ShareAdmin(admin.ModelAdmin):
    list_display = ('user', 'original_post', 'share_text_preview', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('user__username', 'original_post__content', 'share_text')
    date_hierarchy = 'created_at'
    
    def share_text_preview(self, obj):
        return obj.share_text[:50] + '...' if obj.share_text and len(obj.share_text) > 50 else obj.share_text
    share_text_preview.short_description = 'Share Text'


class NotificationAdmin(admin.ModelAdmin):
    list_display = ('recipient', 'sender', 'notification_type', 'is_read', 'created_at')
    list_filter = ('notification_type', 'is_read', 'created_at')
    search_fields = ('recipient__username', 'sender__username')
    readonly_fields = ('is_read',)
    date_hierarchy = 'created_at'
    
    actions = ['mark_as_read', 'mark_as_unread']
    
    def mark_as_read(self, request, queryset):
        updated = queryset.update(is_read=True)
        self.message_user(request, f'{updated} notifications marked as read.')
    mark_as_read.short_description = "Mark selected notifications as read"
    
    def mark_as_unread(self, request, queryset):
        updated = queryset.update(is_read=False)
        self.message_user(request, f'{updated} notifications marked as unread.')
    mark_as_unread.short_description = "Mark selected notifications as unread"


# Unregister the default User admin and register our custom one
admin.site.unregister(User)
admin.site.register(User, UserAdmin)

# Register all models
admin.site.register(Profile, ProfileAdmin)
admin.site.register(Post, PostAdmin)
admin.site.register(Comment, CommentAdmin)
admin.site.register(Reaction, ReactionAdmin)
admin.site.register(Hashtag, HashtagAdmin)
admin.site.register(Follow, FollowAdmin)
admin.site.register(Bookmark, BookmarkAdmin)
admin.site.register(Share, ShareAdmin)
admin.site.register(Notification, NotificationAdmin)
