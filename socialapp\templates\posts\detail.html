{% extends 'base.html' %}

{% block title %}{{ post.user.username }}'s Post - BlogApp{% endblock %}

{% block content %}
<!-- Post Detail -->
<div class="card post-card mb-4">
    <!-- Post Header -->
    <div class="post-header">
        <div class="d-flex align-items-center">
            <img src="{{ post.user.profile.get_avatar_url }}" 
                 alt="{{ post.user.username }}" class="avatar me-3">
            <div class="flex-grow-1">
                <div class="d-flex align-items-center">
                    <h6 class="mb-0 fw-bold">{{ post.user.username }}</h6>
                    {% if post.user.profile.is_verified %}
                    <i class="fas fa-check-circle text-primary ms-1" title="Verified"></i>
                    {% endif %}
                    {% if post.is_edited %}
                    <small class="text-muted ms-2">(edited)</small>
                    {% endif %}
                </div>
                <small class="text-muted">
                    <i class="fas fa-clock"></i> {{ post.created_at|timesince }} ago
                    {% if not post.is_public %}
                    <i class="fas fa-lock ms-2" title="Private post"></i>
                    {% endif %}
                </small>
            </div>
            {% if user == post.user %}
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                        data-bs-toggle="dropdown">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="{% url 'edit_post' post.pk %}">
                        <i class="fas fa-edit"></i> Edit
                    </a></li>
                    <li><a class="dropdown-item text-danger" href="{% url 'delete_post' post.pk %}">
                        <i class="fas fa-trash"></i> Delete
                    </a></li>
                </ul>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Post Content -->
    <div class="post-content">
        <p class="mb-3">{{ post.content|linebreaks }}</p>
        
        <!-- Hashtags -->
        {% if post.hashtags.all %}
        <div class="mb-3">
            {% for hashtag in post.hashtags.all %}
            <a href="{% url 'hashtag_detail' hashtag.slug %}" class="hashtag me-2">
                #{{ hashtag.name }}
            </a>
            {% endfor %}
        </div>
        {% endif %}
        
        <!-- Post Media -->
        {% if post.image %}
        <div class="mb-3">
            <img src="{{ post.image.url }}" alt="Post image" class="img-fluid rounded">
        </div>
        {% endif %}
        
        {% if post.video %}
        <div class="mb-3">
            <video controls class="w-100 rounded">
                <source src="{{ post.video.url }}" type="video/mp4">
                Your browser does not support the video tag.
            </video>
        </div>
        {% endif %}
    </div>

    <!-- Post Stats -->
    <div class="px-3 pb-2">
        <div class="d-flex justify-content-between text-muted small">
            <span>{{ post.likes_count }} likes</span>
            <span>{{ post.comments_count }} comments</span>
            <span>{{ post.shares_count }} shares</span>
            <span>{{ post.views_count }} views</span>
        </div>
    </div>

    <!-- Post Actions -->
    <div class="post-actions">
        <div class="d-flex justify-content-between">
            <!-- Like Button -->
            {% if user.is_authenticated %}
            <form method="post" action="{% url 'toggle_reaction' post.pk %}" class="d-inline">
                {% csrf_token %}
                <input type="hidden" name="reaction_type" value="like">
                <button type="submit" class="action-btn reaction-btn {% if is_liked %}liked{% endif %}" 
                        title="Like">
                    <i class="fas fa-heart"></i>
                </button>
            </form>
            {% else %}
            <button class="action-btn" disabled title="Login to like">
                <i class="fas fa-heart"></i>
            </button>
            {% endif %}

            <!-- Comment Button -->
            <button class="action-btn" onclick="document.getElementById('comment-form').scrollIntoView()" title="Comment">
                <i class="fas fa-comment"></i>
            </button>

            <!-- Share Button -->
            {% if user.is_authenticated %}
            <form method="post" action="{% url 'share_post' post.pk %}" class="d-inline">
                {% csrf_token %}
                <button type="submit" class="action-btn" title="Share">
                    <i class="fas fa-retweet"></i>
                </button>
            </form>
            {% else %}
            <button class="action-btn" disabled title="Login to share">
                <i class="fas fa-retweet"></i>
            </button>
            {% endif %}

            <!-- Bookmark Button -->
            {% if user.is_authenticated %}
            <form method="post" action="{% url 'toggle_bookmark' post.pk %}" class="d-inline">
                {% csrf_token %}
                <button type="submit" class="action-btn {% if is_bookmarked %}bookmarked{% endif %}" 
                        title="Bookmark">
                    <i class="fas fa-bookmark"></i>
                </button>
            </form>
            {% else %}
            <button class="action-btn" disabled title="Login to bookmark">
                <i class="fas fa-bookmark"></i>
            </button>
            {% endif %}
        </div>
    </div>
</div>

<!-- Comments Section -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-comments"></i> Comments ({{ total_comments }})
        </h5>
    </div>
    
    <!-- Add Comment Form -->
    {% if user.is_authenticated %}
    <div class="card-body border-bottom" id="comment-form">
        <form method="post" action="{% url 'add_comment' post.pk %}">
            {% csrf_token %}
            <div class="d-flex">
                <img src="{{ user.profile.get_avatar_url }}" 
                     alt="{{ user.username }}" class="avatar-sm me-3">
                <div class="flex-grow-1">
                    <textarea class="form-control" name="content" rows="3" 
                              placeholder="Write a comment..." maxlength="1000" required></textarea>
                    <div class="d-flex justify-content-between align-items-center mt-2">
                        <small class="text-muted">Max 1000 characters</small>
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="fas fa-paper-plane"></i> Comment
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    {% else %}
    <div class="card-body border-bottom text-center">
        <p class="text-muted mb-0">
            <a href="{% url 'login' %}">Login</a> to leave a comment
        </p>
    </div>
    {% endif %}

    <!-- Comments List -->
    <div class="card-body p-0">
        {% if comments %}
            {% for comment in comments %}
            <div class="p-3 border-bottom">
                <div class="d-flex">
                    <img src="{{ comment.user.profile.get_avatar_url }}" 
                         alt="{{ comment.user.username }}" class="avatar-sm me-3">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-1">
                            <h6 class="mb-0 fw-bold">{{ comment.user.username }}</h6>
                            {% if comment.user.profile.is_verified %}
                            <i class="fas fa-check-circle text-primary ms-1" title="Verified"></i>
                            {% endif %}
                            <small class="text-muted ms-2">
                                {{ comment.created_at|timesince }} ago
                                {% if comment.is_edited %}
                                (edited)
                                {% endif %}
                            </small>
                        </div>
                        <p class="mb-2">{{ comment.content|linebreaks }}</p>
                        
                        <!-- Comment Actions -->
                        <div class="d-flex align-items-center">
                            {% if user.is_authenticated %}
                            <form method="post" action="{% url 'toggle_reaction' post.pk %}" class="d-inline me-3">
                                {% csrf_token %}
                                <input type="hidden" name="reaction_type" value="like">
                                <input type="hidden" name="comment_id" value="{{ comment.id }}">
                                <button type="submit" class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-heart"></i> {{ comment.likes_count }}
                                </button>
                            </form>
                            {% endif %}
                            
                            <button class="btn btn-sm btn-outline-secondary me-2" 
                                    onclick="toggleReplyForm({{ comment.id }})">
                                <i class="fas fa-reply"></i> Reply
                            </button>
                            
                            {% if user == comment.user %}
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                                        data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#">
                                        <i class="fas fa-edit"></i> Edit
                                    </a></li>
                                    <li><a class="dropdown-item text-danger" href="#">
                                        <i class="fas fa-trash"></i> Delete
                                    </a></li>
                                </ul>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Reply Form (Hidden by default) -->
                        <div id="reply-form-{{ comment.id }}" class="mt-3" style="display: none;">
                            <form method="post" action="{% url 'add_comment' post.pk %}">
                                {% csrf_token %}
                                <input type="hidden" name="parent" value="{{ comment.id }}">
                                <div class="d-flex">
                                    <img src="{{ user.profile.get_avatar_url }}" 
                                         alt="{{ user.username }}" class="avatar-sm me-3">
                                    <div class="flex-grow-1">
                                        <textarea class="form-control" name="content" rows="2" 
                                                  placeholder="Write a reply..." maxlength="1000" required></textarea>
                                        <div class="d-flex justify-content-between align-items-center mt-2">
                                            <button type="button" class="btn btn-sm btn-outline-secondary" 
                                                    onclick="toggleReplyForm({{ comment.id }})">
                                                Cancel
                                            </button>
                                            <button type="submit" class="btn btn-primary btn-sm">
                                                <i class="fas fa-paper-plane"></i> Reply
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Replies -->
                        {% if comment.replies.all %}
                        <div class="mt-3">
                            {% for reply in comment.replies.all %}
                            <div class="d-flex mt-2">
                                <img src="{{ reply.user.profile.get_avatar_url }}" 
                                     alt="{{ reply.user.username }}" class="avatar-sm me-3">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center mb-1">
                                        <h6 class="mb-0 fw-bold">{{ reply.user.username }}</h6>
                                        {% if reply.user.profile.is_verified %}
                                        <i class="fas fa-check-circle text-primary ms-1" title="Verified"></i>
                                        {% endif %}
                                        <small class="text-muted ms-2">
                                            {{ reply.created_at|timesince }} ago
                                            {% if reply.is_edited %}
                                            (edited)
                                            {% endif %}
                                        </small>
                                    </div>
                                    <p class="mb-1">{{ reply.content|linebreaks }}</p>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}

            <!-- Pagination for Comments -->
            {% if comments.has_other_pages %}
            <div class="p-3">
                <nav aria-label="Comments pagination">
                    <ul class="pagination justify-content-center mb-0">
                        {% if comments.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ comments.previous_page_number }}">
                                <i class="fas fa-chevron-left"></i> Previous
                            </a>
                        </li>
                        {% endif %}

                        {% for num in comments.paginator.page_range %}
                            {% if comments.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                            {% elif num > comments.number|add:'-3' and num < comments.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                            </li>
                            {% endif %}
                        {% endfor %}

                        {% if comments.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ comments.next_page_number }}">
                                Next <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}

        {% else %}
        <div class="p-4 text-center">
            <i class="fas fa-comments fa-2x text-muted mb-3"></i>
            <h6>No comments yet</h6>
            <p class="text-muted mb-0">
                {% if user.is_authenticated %}
                Be the first to comment on this post!
                {% else %}
                <a href="{% url 'login' %}">Login</a> to leave the first comment.
                {% endif %}
            </p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function toggleReplyForm(commentId) {
    const replyForm = document.getElementById(`reply-form-${commentId}`);
    if (replyForm.style.display === 'none') {
        replyForm.style.display = 'block';
        replyForm.querySelector('textarea').focus();
    } else {
        replyForm.style.display = 'none';
    }
}

// Auto-hide reply forms when clicking outside
document.addEventListener('click', function(e) {
    if (!e.target.closest('.reply-form')) {
        const replyForms = document.querySelectorAll('[id^="reply-form-"]');
        replyForms.forEach(form => {
            form.style.display = 'none';
        });
    }
});
</script>
{% endblock %} 